using Model.Responsible.List.Response;
using System;
using System.Collections.Generic;

namespace Application.Tests.Responsible.Builders
{
    public static class ListResponsibleResponseBuilder
    {
        public static IEnumerable<ListResponsibleResponse> CreateDefault()
        {
            return new List<ListResponsibleResponse>()
            {
                new()
                {
                    Id = Guid.NewGuid(),
                    Name = "Lorem"
                }
            };
        }
    }
}
