using Application.ClientUnit.Search;
using Application.Core;
using Application.Tests.Client.Builders;
using Application.Tests.ClientUnit.Builders;
using Database.Repositories.ClientUnit;
using Database.Repositories.User;
using FluentAssertions;
using Model.ClientUnit.Search.Request;
using Model.ClientUnit.Search.Response;
using Moq;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Xunit;

namespace Application.Tests.ClientUnit
{
    [Trait("SearchClientUnitUseCase", "Execute")]
    public class SearchTests
    {
        private readonly Mock<IClientUnitRepository> _clientUnitRepository;
        private readonly Mock<IUserRepository> _userRepository;
        private readonly ISearchClientUnitUseCase _searchClientUnitCase;

        public SearchTests()
        {
            _clientUnitRepository = new Mock<IClientUnitRepository>();
            _userRepository = new Mock<IUserRepository>();
            _searchClientUnitCase = new SearchClientUnitUseCase(
                _clientUnitRepository.Object,
                _userRepository.Object);
        }

        [Fact(DisplayName = "WhenRequestIsInvalid_ReturnsBadRequest")]
        public async Task Execute_WhenRequestIsInvalid_ReturnsBadRequest()
        {
            _clientUnitRepository
               .Setup(x => x.SearchAsync(It.IsAny<SearchClientUnitRequest>()))
               .ReturnsAsync(default(IEnumerable<SearchClientUnitResponse>));

            var request = SearchClientUnitRequestBuilder
                .CreateDefault()
                .WithRequest(false, Guid.Empty, DateTime.Now, DateTime.Now, string.Empty, 0, 0, 1);

            var useCaseResponse = await _searchClientUnitCase.Execute(request);

            useCaseResponse.Status.Should().Be(UseCaseResponseKind.BadRequest);
        }

        [Fact(DisplayName = "WhenAnUnexpectedExceptionIsThrow_ReturnsInternalServerError")]
        public async Task Execute_WhenAnUnexpectedExceptionIsThrow_ReturnsInternalServerError()
        {
            _clientUnitRepository
               .Setup(x => x.SearchAsync(It.IsAny<SearchClientUnitRequest>()))
               .Throws(new Exception());

            var useCaseResponse = await _searchClientUnitCase.Execute(SearchClientUnitRequestBuilder
                .CreateDefault());

            useCaseResponse.Status.Should().Be(UseCaseResponseKind.InternalServerError);
        }

        [Fact(DisplayName = "WhenRequestIsRight_ReturnsOK")]
        public async Task Execute_WhenRequestIsRight_ReturnsOK()
        {
            _clientUnitRepository
               .Setup(x => x.SearchAsync(It.IsAny<SearchClientUnitRequest>()))
                .ReturnsAsync(SearchClientUnitResponseBuilder.CreateDefault());

            var useCaseResponse = await _searchClientUnitCase.Execute(SearchClientUnitRequestBuilder
                .CreateDefault());

            useCaseResponse.Status.Should().Be(UseCaseResponseKind.OK);
        }
    }
}
