using Application.Area.Update;
using Application.Core;
using Application.Tests.Area.Builders;
using Database.Repositories.Area;
using FluentAssertions;
using Moq;
using System;
using System.Threading.Tasks;
using Xunit;

namespace Application.Tests.Area
{
    [Trait("UpdateAreaUseCase", "Execute")]
    public class UpdateTests
    {
        private readonly Mock<IAreaRepository> _areaRepository;
        private readonly IUpdateAreaUseCase _updateAreaUseCase;

        public UpdateTests()
        {
            _areaRepository = new Mock<IAreaRepository>();
            _updateAreaUseCase = new UpdateAreaUseCase(_areaRepository.Object);
        }

        [Fact(DisplayName = "WhenRequestIsInvalid_ReturnsBadRequest")]
        public async Task Execute_WhenRequestIsInvalid_ReturnsBadRequest()
        {
            _areaRepository
                .Setup(x => x.GetAsync(It.IsAny<Guid>()))
                .ReturnsAsync(default(Domain.Entities.Area));

            _areaRepository
                .Setup(x => x.UpdateAsync(It.IsAny<Domain.Entities.Area>()))
                .Returns(Task.CompletedTask);

            var request = UpdateAreaRequestBuilder
                .CreateDefault()
                .WithRequest(Guid.Empty, null);

            var useCaseResponse = await _updateAreaUseCase
                .Execute(request);

            useCaseResponse.Status.Should().Be(UseCaseResponseKind.BadRequest);
        }

        [Fact(DisplayName = "WhenAnUnexpectedExceptionIsThrow_ReturnsInternalServerError")]
        public async Task Execute_WhenAnUnexpectedExceptionIsThrow_ReturnsInternalServerError()
        {
            _areaRepository
                .Setup(x => x.GetAsync(It.IsAny<Guid>()))
                 .ThrowsAsync(new Exception());

            _areaRepository
                .Setup(x => x.UpdateAsync(It.IsAny<Domain.Entities.Area>()))
                .Returns(Task.CompletedTask);

            var request = UpdateAreaRequestBuilder
                .CreateDefault();

            var useCaseResponse = await _updateAreaUseCase
                .Execute(request);

            useCaseResponse.Status.Should().Be(UseCaseResponseKind.InternalServerError);
        }

        [Fact(DisplayName = "WhenAreaNameAlreadyExists_ReturnsBadRequest")]
        public async Task Execute_WhenAreaNameAlreadyExists_ReturnsBadRequest()
        {
            var request = UpdateAreaRequestBuilder
               .CreateDefault();

            _areaRepository
                .Setup(x => x.NameExists(request.Name, request.Id))
                .ReturnsAsync(true);

            _areaRepository
                .Setup(x => x.UpdateAsync(It.IsAny<Domain.Entities.Area>()))
                .Returns(Task.CompletedTask);

            var useCaseResponse = await _updateAreaUseCase
                .Execute(request);

            useCaseResponse.Status.Should().Be(UseCaseResponseKind.BadRequest);
        }

        [Fact(DisplayName = "WhenRequestIsRight_ReturnsOK")]
        public async Task Execute_WhenRequestIsRight_ReturnsOK()
        {
            _areaRepository
                .Setup(x => x.GetAsync(It.IsAny<Guid>()))
                .ReturnsAsync(new Domain.Entities.Area());

            _areaRepository
                .Setup(x => x.UpdateAsync(It.IsAny<Domain.Entities.Area>()))
                .Returns(Task.CompletedTask);

            var request = UpdateAreaRequestBuilder
                .CreateDefault();

            _areaRepository
                .Setup(x => x.NameExists(request.Name, request.Id))
                .ReturnsAsync(false);

            var useCaseResponse = await _updateAreaUseCase.Execute(request);

            useCaseResponse.Status.Should().Be(UseCaseResponseKind.OK);
        }
    }
}
