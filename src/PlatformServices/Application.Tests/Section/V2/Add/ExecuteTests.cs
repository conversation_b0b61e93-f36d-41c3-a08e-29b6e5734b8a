using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Application.Core;
using Application.Section.V2.Add;
using Application.Services.BlobStorage;
using Application.Tests._Shared;
using Application.Tests.Instrument.Builders;
using Application.Tests.Section.Builders;
using Application.Tests.Structure.Builders;
using Coordinate.Core.Enums;
using Database.Repositories.Instrument;
using Database.Repositories.Section;
using Database.Repositories.StaticMaterial;
using Database.Repositories.Structure;
using Database.Repositories.User;
using Model.Section.V2.Add.Request;

namespace Application.Tests.Section.V2.Add;

[Trait("AddSectionUseCase (v2)", "Execute")]
public class ExecuteTests
{
    private readonly Mock<IStructureRepository> _structureRepository = new();
    private readonly Mock<ISectionRepository> _sectionRepository = new();
    private readonly Mock<IInstrumentRepository> _instrumentRepository = new();
    private readonly Mock<IBlobStorageService> _blobService = new();
    private readonly Mock<IUserRepository> _userRepository = new();

    private readonly Mock<IStaticMaterialRepository> _staticMaterialRepository =
        new();

    private readonly AddSectionUseCase _useCase;

    public ExecuteTests()
    {
        _useCase = new AddSectionUseCase(
            _structureRepository.Object,
            _sectionRepository.Object,
            _instrumentRepository.Object,
            _staticMaterialRepository.Object,
            _blobService.Object,
            _userRepository.Object);
    }

    [Fact(DisplayName = "When request is null, then returns bad request")]
    public async Task WhenRequestIsNull_ReturnsBadRequest()
    {
        AddSectionRequestV2 request = null;

        var response = await _useCase.Execute(request);

        response.Status.Should().Be(UseCaseResponseKind.BadRequest);
    }

    [Fact(DisplayName = "When request is invalid, then returns bad request")]
    public async Task WhenRequestIsInvalid_ReturnsBadRequest()
    {
        var request = new AddSectionRequestV2();

        var response = await _useCase.Execute(request);

        response.Status.Should().Be(UseCaseResponseKind.BadRequest);
    }

    [Fact(
        DisplayName =
            "When Section name already exists, then returns bad request")]
    public async Task WhenSectionNameAlreadyExists_ReturnsBadRequest()
    {
        _sectionRepository
            .Setup(x => x.NameExists(It.IsAny<string>(), It.IsAny<Guid>()))
            .ReturnsAsync(true);

        var request = AddSectionRequestV2Builder
            .CreateDefault()
            .WithRequester(Guid.NewGuid());

        var useCaseResponse = await _useCase.Execute(request);

        useCaseResponse.Status.Should().Be(UseCaseResponseKind.BadRequest);
    }

    [Fact(
        DisplayName =
            "When Section name already exists, then returns error message")]
    public async Task WhenSectionNameAlreadyExists_ReturnsErrorMessage()
    {
        _sectionRepository
            .Setup(x => x.NameExists(It.IsAny<string>(), It.IsAny<Guid>()))
            .ReturnsAsync(true);

        var request = AddSectionRequestV2Builder
            .CreateDefault()
            .WithRequester(Guid.NewGuid());

        var useCaseResponse = await _useCase.Execute(request);

        useCaseResponse.Errors
            .First()
            .Message
            .Should()
            .Be("Section name already exists on this structure.");
    }

    [Fact(
        DisplayName =
            "When structure does not exist, then returns bad request")]
    public async Task WhenStructureDoesNotExist_ReturnsBadRequest()
    {
        _sectionRepository
            .Setup(x => x.NameExists(It.IsAny<string>(), It.IsAny<Guid>()))
            .ReturnsAsync(false);

        var request = AddSectionRequestV2Builder
            .CreateDefault()
            .WithRequester(Guid.NewGuid());

        var useCaseResponse = await _useCase.Execute(request);

        useCaseResponse.Status.Should().Be(UseCaseResponseKind.BadRequest);
    }

    [Fact(
        DisplayName =
            "When structure does not exist, then returns error message")]
    public async Task WhenStructureDoesNotExist_ReturnsErrorMessage()
    {
        _sectionRepository
            .Setup(x => x.NameExists(It.IsAny<string>(), It.IsAny<Guid>()))
            .ReturnsAsync(false);

        var request = AddSectionRequestV2Builder
            .CreateDefault()
            .WithRequester(Guid.NewGuid());

        var useCaseResponse = await _useCase.Execute(request);

        useCaseResponse.Errors
            .First()
            .Message
            .Should()
            .Be("The structure was not found in the database.");
    }

    [Fact(
        DisplayName =
            "When structure datum is different, then returns bad request")]
    public async Task WhenStructureDatumIsDifferent_ReturnsBadRequest()
    {
        _sectionRepository
            .Setup(x => x.NameExists(It.IsAny<string>(), It.IsAny<Guid>()))
            .ReturnsAsync(false);

        var structure = StructureBuilder
            .CreateDefault()
            .WithDatum(Datum.WGS84);

        _structureRepository
            .Setup(repo => repo.GetBasicAsync(It.IsAny<Guid>()))
            .ReturnsAsync(structure);

        var request = AddSectionRequestV2Builder
            .CreateDefault()
            .WithRequester(Guid.NewGuid());

        var useCaseResponse = await _useCase.Execute(request);

        useCaseResponse.Status.Should().Be(UseCaseResponseKind.BadRequest);
    }

    [Fact(
        DisplayName =
            "When structure datum is different, then returns error message")]
    public async Task WhenStructureDatumIsDifferent_ReturnsErrorMessage()
    {
        _sectionRepository
            .Setup(x => x.NameExists(It.IsAny<string>(), It.IsAny<Guid>()))
            .ReturnsAsync(false);

        var structure = StructureBuilder
            .CreateDefault()
            .WithDatum(Datum.WGS84);

        _structureRepository
            .Setup(repo => repo.GetBasicAsync(It.IsAny<Guid>()))
            .ReturnsAsync(structure);

        var request = AddSectionRequestV2Builder
            .CreateDefault()
            .WithRequester(Guid.NewGuid());

        var useCaseResponse = await _useCase.Execute(request);

        useCaseResponse.Errors
            .First()
            .Message
            .Should()
            .Be("Structure and section datum must be the same.");
    }

    [Fact(
        DisplayName =
            "When an instrument does not exist, then returns bad request")]
    public async Task WhenInstrumentDoesNotExist_ReturnsBadRequest()
    {
        _sectionRepository
            .Setup(x => x.NameExists(It.IsAny<string>(), It.IsAny<Guid>()))
            .ReturnsAsync(false);

        _structureRepository
            .Setup(repo => repo.GetBasicAsync(It.IsAny<Guid>()))
            .ReturnsAsync(StructureBuilder.CreateDefault());

        _instrumentRepository
            .Setup(repo => repo.GetByIdsAsync(
                It.IsAny<IEnumerable<Guid>>(),
                It.IsAny<Guid>(),
                true))
            .ReturnsAsync(new List<Domain.Entities.Instrument>());

        var request = AddSectionRequestV2Builder
            .CreateDefault()
            .WithRequester(Guid.NewGuid());

        var useCaseResponse = await _useCase.Execute(request);

        useCaseResponse.Status.Should().Be(UseCaseResponseKind.BadRequest);
    }

    [Fact(
        DisplayName =
            "When an instrument does not exist, then returns error message")]
    public async Task WhenInstrumentDoesNotExist_ReturnsErrorMessage()
    {
        _sectionRepository
            .Setup(x => x.NameExists(It.IsAny<string>(), It.IsAny<Guid>()))
            .ReturnsAsync(false);

        _structureRepository
            .Setup(repo => repo.GetBasicAsync(It.IsAny<Guid>()))
            .ReturnsAsync(StructureBuilder.CreateDefault());

        _instrumentRepository
            .Setup(repo => repo.GetByIdsAsync(
                It.IsAny<IEnumerable<Guid>>(),
                It.IsAny<Guid>(),
                true))
            .ReturnsAsync(new List<Domain.Entities.Instrument>());

        var request = AddSectionRequestV2Builder
            .CreateDefault()
            .WithRequester(Guid.NewGuid());

        var useCaseResponse = await _useCase.Execute(request);

        useCaseResponse.Errors
            .First()
            .Message
            .Should()
            .Be("The instrument was not found in the database.");
    }

    [Fact(
        DisplayName =
            "When an unhandled exception is thrown, then returns internal server error")]
    public async Task WhenExceptionIsThrown_ReturnsInternalServerError()
    {
        _sectionRepository
            .Setup(x => x.NameExists(It.IsAny<string>(), It.IsAny<Guid>()))
            .ReturnsAsync(false);

        _structureRepository
            .Setup(repo => repo.GetBasicAsync(It.IsAny<Guid>()))
            .ReturnsAsync(StructureBuilder.CreateDefault());

        var request = AddSectionRequestV2Builder
            .CreateDefault()
            .WithRequester(Guid.NewGuid());

        var instruments = request.GeneralInformation.Instruments
            .Select(i => InstrumentBuilder
                .CreateElectricPiezometer()
                .WithId(i.Id))
            .ToList();

        _instrumentRepository
            .Setup(repo => repo.GetByIdsAsync(
                It.IsAny<IEnumerable<Guid>>(),
                It.IsAny<Guid>(),
                true))
            .ThrowsAsync(new Exception());

        var useCaseResponse = await _useCase.Execute(request);

        useCaseResponse.Status
            .Should()
            .Be(UseCaseResponseKind.InternalServerError);
    }

    [Fact(DisplayName = "When request is valid, then returns Created")]
    public async Task WhenDxfFileIsInvalid_ReturnsCreated()
    {
        _sectionRepository
            .Setup(x => x.NameExists(It.IsAny<string>(), It.IsAny<Guid>()))
            .ReturnsAsync(false);

        _structureRepository
            .Setup(repo => repo.GetBasicAsync(It.IsAny<Guid>()))
            .ReturnsAsync(StructureBuilder.CreateDefault());

        var request = AddSectionRequestV2Builder
            .CreateDefault()
            .WithRequester(Guid.NewGuid());

        var instruments = request.GeneralInformation.Instruments
            .Select(i => InstrumentBuilder
                .CreateElectricPiezometer()
                .WithId(i.Id))
            .ToList();

        _instrumentRepository
            .Setup(repo => repo.GetByIdsAsync(
                It.IsAny<IEnumerable<Guid>>(),
                It.IsAny<Guid>(),
                true))
            .ReturnsAsync(instruments);

        var useCaseResponse = await _useCase.Execute(request);

        useCaseResponse.Status.Should().Be(UseCaseResponseKind.Created);
    }
}
