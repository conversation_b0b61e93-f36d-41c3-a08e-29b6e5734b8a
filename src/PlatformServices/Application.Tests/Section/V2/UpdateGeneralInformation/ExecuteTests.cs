using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Application.Core;
using Application.Section.V2.UpdateGeneralInformation;
using Application.Tests._Shared;
using Application.Tests.Instrument.Builders;
using Application.Tests.Section.Builders;
using Application.Tests.Structure.Builders;
using Coordinate.Core.Enums;
using Database.Repositories.Instrument;
using Database.Repositories.Section;
using Database.Repositories.Structure;
using Database.Repositories.User;
using Model.Section.V2.UpdateGeneralInformation.Request;

namespace Application.Tests.Section.V2.UpdateGeneralInformation;

[Trait("UpdateGeneralInformationUseCase", "Execute")]
public class ExecuteTests
{
    private readonly Mock<ISectionRepository> _sectionRepository = new();
    private readonly Mock<IUserRepository> _userRepository = new();
    private readonly Mock<IStructureRepository> _structureRepository = new();
    private readonly Mock<IInstrumentRepository> _instrumentRepository = new();

    private readonly UpdateGeneralInformationUseCase _useCase;

    public ExecuteTests()
    {
        _useCase = new UpdateGeneralInformationUseCase(
            _sectionRepository.Object,
            _userRepository.Object,
            _structureRepository.Object,
            _instrumentRepository.Object);
    }

    [Fact(DisplayName = "When request is null, then returns bad request")]
    public async Task WhenRequestIsNull_ReturnsBadRequest()
    {
        UpdateGeneralInformationRequest request = null;

        var response = await _useCase.Execute(request);

        response.Status.Should().Be(UseCaseResponseKind.BadRequest);
    }

    [Fact(DisplayName = "When request is invalid, then returns bad request")]
    public async Task WhenRequestIsInvalid_ReturnsBadRequest()
    {
        var request = UpdateGeneralInformationRequestBuilder
            .CreateDefault();

        var response = await _useCase.Execute(request);

        response.Status.Should().Be(UseCaseResponseKind.BadRequest);
    }

    [Fact(
        DisplayName = "When section does not exist, then returns bad request")]
    public async Task WhenSectionDoesNotExist_ReturnsBadRequest()
    {
        _sectionRepository
            .Setup(repo => repo.GetAsync(It.IsAny<Guid>()))
            .ReturnsAsync((Domain.Entities.Section)null);

        var request = UpdateGeneralInformationRequestBuilder
            .CreateDefault()
            .WithRequester(Guid.NewGuid());

        var response = await _useCase.Execute(request);

        response.Status.Should().Be(UseCaseResponseKind.BadRequest);
    }

    [Fact(
        DisplayName =
            "When section does not exist, then returns error message")]
    public async Task WhenSectionDoesNotExist_ReturnsErrorMessage()
    {
        _sectionRepository
            .Setup(repo => repo.GetAsync(It.IsAny<Guid>()))
            .ReturnsAsync((Domain.Entities.Section)null);

        var request = UpdateGeneralInformationRequestBuilder
            .CreateDefault()
            .WithRequester(Guid.NewGuid());

        var response = await _useCase.Execute(request);

        response.Errors
            .First()
            .Message
            .Should()
            .Be("Section not found in database.");
    }

    [Fact(
        DisplayName =
            "When section name already exists, then returns bad request")]
    public async Task WhenSectionNameAlreadyExists_ReturnsBadRequest()
    {
        _sectionRepository
            .Setup(repo => repo.GetAsync(It.IsAny<Guid>()))
            .ReturnsAsync(SectionBuilder.CreateDefault());

        _sectionRepository
            .Setup(repo => repo.NameExists(
                It.IsAny<string>(),
                It.IsAny<Guid>(),
                It.IsAny<Guid>()))
            .ReturnsAsync(true);

        var request = UpdateGeneralInformationRequestBuilder
            .CreateDefault()
            .WithRequester(Guid.NewGuid());

        var response = await _useCase.Execute(request);

        response.Status.Should().Be(UseCaseResponseKind.BadRequest);
    }
    
    [Fact(
        DisplayName =
            "When section name already exists, then returns error message")]
    public async Task WhenSectionNameAlreadyExists_ReturnsErrorMessage()
    {
        _sectionRepository
            .Setup(repo => repo.GetAsync(It.IsAny<Guid>()))
            .ReturnsAsync(SectionBuilder.CreateDefault());

        _sectionRepository
            .Setup(repo => repo.NameExists(
                It.IsAny<string>(),
                It.IsAny<Guid>(),
                It.IsAny<Guid>()))
            .ReturnsAsync(true);

        var request = UpdateGeneralInformationRequestBuilder
            .CreateDefault()
            .WithRequester(Guid.NewGuid());

        var response = await _useCase.Execute(request);

        response.Errors
            .First()
            .Message
            .Should()
            .Be("Section name already exists on this structure.");
    }

    [Fact(
        DisplayName =
            "When structure does not exist, then returns bad request")]
    public async Task WhenStructureDoesNotExist_ReturnsBadRequest()
    {
        _sectionRepository
            .Setup(repo => repo.GetAsync(It.IsAny<Guid>()))
            .ReturnsAsync(SectionBuilder.CreateDefault());

        _structureRepository
            .Setup(repo => repo.GetBasicAsync(It.IsAny<Guid>()))
            .ReturnsAsync((Domain.Entities.Structure)null);

        var request = UpdateGeneralInformationRequestBuilder
            .CreateDefault()
            .WithRequester(Guid.NewGuid());

        var response = await _useCase.Execute(request);

        response.Status.Should().Be(UseCaseResponseKind.BadRequest);
    }
    
    [Fact(
        DisplayName =
            "When structure does not exist, then returns error message")]
    public async Task WhenStructureDoesNotExist_ReturnsErrorMessage()
    {
        _sectionRepository
            .Setup(repo => repo.GetAsync(It.IsAny<Guid>()))
            .ReturnsAsync(SectionBuilder.CreateDefault());

        _structureRepository
            .Setup(repo => repo.GetBasicAsync(It.IsAny<Guid>()))
            .ReturnsAsync((Domain.Entities.Structure)null);

        var request = UpdateGeneralInformationRequestBuilder
            .CreateDefault()
            .WithRequester(Guid.NewGuid());

        var response = await _useCase.Execute(request);

        response.Errors
            .First()
            .Message
            .Should()
            .Be("The structure was not found in the database.");
    }

    [Fact(
        DisplayName =
            "When structure datum is different, then returns bad request")]
    public async Task WhenStructureDatumIsDifferent_ReturnsBadRequest()
    {
        _sectionRepository
            .Setup(repo => repo.GetAsync(It.IsAny<Guid>()))
            .ReturnsAsync(SectionBuilder.CreateDefault());

        var structure = StructureBuilder
            .CreateDefault()
            .WithDatum(Datum.WGS84);

        _structureRepository
            .Setup(repo => repo.GetBasicAsync(It.IsAny<Guid>()))
            .ReturnsAsync(structure);

        var request = UpdateGeneralInformationRequestBuilder
            .CreateDefault()
            .WithRequester(Guid.NewGuid());

        var response = await _useCase.Execute(request);

        response.Status.Should().Be(UseCaseResponseKind.BadRequest);
    }
    
    [Fact(
        DisplayName =
            "When structure datum is different, then returns error message")]
    public async Task WhenStructureDatumIsDifferent_ReturnsErrorMessage()
    {
        _sectionRepository
            .Setup(repo => repo.GetAsync(It.IsAny<Guid>()))
            .ReturnsAsync(SectionBuilder.CreateDefault());

        var structure = StructureBuilder
            .CreateDefault()
            .WithDatum(Datum.WGS84);

        _structureRepository
            .Setup(repo => repo.GetBasicAsync(It.IsAny<Guid>()))
            .ReturnsAsync(structure);

        var request = UpdateGeneralInformationRequestBuilder
            .CreateDefault()
            .WithRequester(Guid.NewGuid());

        var response = await _useCase.Execute(request);

        response.Errors
            .First()
            .Message
            .Should()
            .Be("Structure and section datum must be the same.");
    }

    [Fact(
        DisplayName =
            "When an instrument does not exist, then returns bad request")]
    public async Task WhenInstrumentDoesNotExist_ReturnsBadRequest()
    {
        _sectionRepository
            .Setup(repo => repo.GetAsync(It.IsAny<Guid>()))
            .ReturnsAsync(SectionBuilder.CreateDefault());

        _structureRepository
            .Setup(repo => repo.GetBasicAsync(It.IsAny<Guid>()))
            .ReturnsAsync(StructureBuilder.CreateDefault());

        _instrumentRepository
            .Setup(repo => repo.GetByIdsAsync(
                It.IsAny<IEnumerable<Guid>>(),
                It.IsAny<Guid>(),
                true))
            .ReturnsAsync(new List<Domain.Entities.Instrument>());

        var request = UpdateGeneralInformationRequestBuilder
            .CreateDefault()
            .WithRequester(Guid.NewGuid());

        var response = await _useCase.Execute(request);

        response.Status.Should().Be(UseCaseResponseKind.BadRequest);
    }

    [Fact(
        DisplayName =
            "When an instrument does not exist, then returns error message")]
    public async Task WhenInstrumentDoesNotExist_ReturnsErrorMessage()
    {
        _sectionRepository
            .Setup(repo => repo.GetAsync(It.IsAny<Guid>()))
            .ReturnsAsync(SectionBuilder.CreateDefault());

        _structureRepository
            .Setup(repo => repo.GetBasicAsync(It.IsAny<Guid>()))
            .ReturnsAsync(StructureBuilder.CreateDefault());

        _instrumentRepository
            .Setup(repo => repo.GetByIdsAsync(
                It.IsAny<IEnumerable<Guid>>(),
                It.IsAny<Guid>(),
                true))
            .ReturnsAsync(new List<Domain.Entities.Instrument>());

        var request = UpdateGeneralInformationRequestBuilder
            .CreateDefault()
            .WithRequester(Guid.NewGuid());

        var response = await _useCase.Execute(request);

        response.Errors
            .First()
            .Message
            .Should()
            .Be("The instrument was not found in the database.");
    }

    [Fact(
        DisplayName =
            "When an unhandled exception is thrown, then returns internal server error")]
    public async Task WhenExceptionIsThrown_ReturnsInternalServerError()
    {
        _sectionRepository
            .Setup(repo => repo.GetAsync(It.IsAny<Guid>()))
            .ThrowsAsync(new Exception());

        var request = UpdateGeneralInformationRequestBuilder
            .CreateDefault()
            .WithRequester(Guid.NewGuid());

        var response = await _useCase.Execute(request);

        response.Status.Should().Be(UseCaseResponseKind.InternalServerError);
    }

    [Fact(DisplayName = "When request is valid, then returns OK")]
    public async Task WhenRequestIsValid_ReturnsOk()
    {
        _sectionRepository
            .Setup(repo => repo.GetAsync(It.IsAny<Guid>()))
            .ReturnsAsync(SectionBuilder.CreateDefault());

        _structureRepository
            .Setup(repo => repo.GetBasicAsync(It.IsAny<Guid>()))
            .ReturnsAsync(StructureBuilder.CreateDefault());
        
        var request = UpdateGeneralInformationRequestBuilder
            .CreateDefault()
            .WithRequester(Guid.NewGuid());
        
        var instruments = request.GeneralInformation.Instruments
            .Select(i => InstrumentBuilder
                .CreateElectricPiezometer()
                .WithId(i.Id))
            .ToList();

        _instrumentRepository
            .Setup(repo => repo.GetByIdsAsync(
                It.IsAny<IEnumerable<Guid>>(),
                It.IsAny<Guid>(),
                true))
            .ReturnsAsync(instruments);

        var response = await _useCase.Execute(request);

        response.Status.Should().Be(UseCaseResponseKind.OK);
    }
}
