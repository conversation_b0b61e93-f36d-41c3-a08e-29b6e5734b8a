using Bogus;
using Domain.Enums;
using Model.Report.Update;
using System;
using System.Linq;

namespace Application.Tests.Report.Builders
{
    internal static class UpdateReportRequestBuilder
    {
        internal static UpdateReportRequest CreateDefault()
        {
            return default;
        }

        internal static UpdateReportRequest CreateValid(
            ReportPeriodicityType periodicityType,
            bool requestedBySuperSupport = true)
        {
            var userStructures = Enumerable.Range(start: 0, count: 2).Select(_ => Guid.NewGuid());

            return new Faker<UpdateReportRequest>()
                .CustomInstantiator(faker =>
                    new UpdateReportRequest
                    {
                        DaysToAnalyze = faker.Random.Number(min: 1, max: 365),
                        DailyReportParameters = SaveDailyReportRequestBuilder.CreateValid(),
                        DestinationEmails = Enumerable.Range(start: 0, count: 2).Select(_ => faker.Internet.Email(provider: "ivoryit.com.br")),
                        MonthlyReportParameters = SaveMonthlyReportRequestBuilder.CreateValid(),
                        PeriodicityType = periodicityType,
                        RequestedBy = Guid.NewGuid(),
                        RequestedBySuperSupport = requestedBySuperSupport,
                        RequestedUserStructures = userStructures.ToList(),
                        ResponsibleName = faker.Person.FullName,
                        StructureId = userStructures.First(),
                        SubjectType = ReportSubjectType.EoR,
                        Title = faker.Lorem.Sentence(wordCount: 2),
                        WeeklyReportParameters = SaveWeeklyReportRequestBuilder.CreateValid(),
                    })
                .Generate();
        }
    }
}
