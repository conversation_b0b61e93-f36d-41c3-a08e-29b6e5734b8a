using Domain.Entities;
using System;
using System.Collections.Generic;

namespace Application.Tests.Chart.Builder
{
    public static class GetClimateInstrumentDataResponseBuilder
    {
        public static List<ReadingValue> CreateDefault()
        {
            return new()
            {
                new()
                {
                    Id = Guid.NewGuid(),
                    Pluviometry = 15,
                    Date = DateTime.UtcNow
                }
            };
        }
    }
}
