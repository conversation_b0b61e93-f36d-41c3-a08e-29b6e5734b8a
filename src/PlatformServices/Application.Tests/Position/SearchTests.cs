using Application.Core;
using Application.Position.Search;
using Application.Tests.Position.Builders;
using Database.Repositories.Position;
using FluentAssertions;
using Model.Position.Search.Request;
using Model.Position.Search.Response;
using Moq;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Xunit;

namespace Application.Tests.Position
{
    [Trait("SearchPositionUseCase", "Execute")]
    public class SearchTests
    {
        private readonly Mock<IPositionRepository> _positionRepository;
        private readonly ISearchPositionUseCase _searchPositionUseCase;

        public SearchTests()
        {
            _positionRepository = new Mock<IPositionRepository>();
            _searchPositionUseCase = new SearchPositionUseCase(_positionRepository.Object);
        }

        [Fact(DisplayName = "WhenRequestIsInvalid_ReturnsBadRequest")]
        public async Task Execute_WhenRequestIsInvalid_ReturnsBadRequest()
        {
            _positionRepository
               .Setup(x => x.SearchAsync(It.IsAny<SearchPositionRequest>()))
               .ReturnsAsync(default(IEnumerable<SearchPositionResponse>));

            var request = SearchPositionRequestBuilder
                .CreateDefault()
                .WithRequest(string.Empty, 0, 0, 0);

            var useCaseResponse = await _searchPositionUseCase
                .Execute(request);

            useCaseResponse.Status.Should().Be(UseCaseResponseKind.BadRequest);
        }

        [Fact(DisplayName = "WhenAnUnexpectedExceptionIsThrow_ReturnsInternalServerError")]
        public async Task Execute_WhenAnUnexpectedExceptionIsThrow_ReturnsInternalServerError()
        {
            _positionRepository
               .Setup(x => x.SearchAsync(It.IsAny<SearchPositionRequest>()))
               .Throws(new Exception());

            var useCaseResponse = await _searchPositionUseCase
                .Execute(SearchPositionRequestBuilder
                .CreateDefault());

            useCaseResponse.Status.Should().Be(UseCaseResponseKind.InternalServerError);
        }

        [Fact(DisplayName = "WhenRequestIsValid_ReturnsOK")]
        public async Task Execute_WhenRequestIsValid_ReturnsOk()
        {
            _positionRepository
               .Setup(x => x.SearchAsync(It.IsAny<SearchPositionRequest>()))
               .ReturnsAsync(SearchPositionResponseBuilder.CreateDefault());

            var request = SearchPositionRequestBuilder
                .CreateDefault();

            var useCaseResponse = await _searchPositionUseCase
                .Execute(request);

            useCaseResponse.Status.Should().Be(UseCaseResponseKind.OK);
        }
    }
}
