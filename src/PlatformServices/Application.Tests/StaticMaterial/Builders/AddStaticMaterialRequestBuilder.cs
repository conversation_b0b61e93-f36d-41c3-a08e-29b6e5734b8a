using Domain.Enums;
using Model.StaticMaterial.Add.Request;
using System;

namespace Application.Tests.StaticMaterial.Builders
{
    public static class AddStaticMaterialRequestBuilder
    {
        public static AddStaticMaterialRequest CreateMohrCoulomb()
        {
            return new()
            {
                RequestedBy = Guid.NewGuid(),
                RequestedBySuperSupport = true,
                StaticMaterial = new()
                {
                    Name = "MC-01",
                    Structure = new() { Id = Guid.NewGuid() },
                    DrainedStaticMaterialValue = new()
                    {
                        ConstitutiveModel = ConstitutiveModel.MohrCoulomb,
                        Cohesion = 1,
                        FrictionAngle = 125.123,
                        TensileStrength = 1,
                        Color = "#ffffff",
                        WaterSurface = WaterSurface.None,
                        Hu = Hu.Auto
                    },
                    UndrainedStaticMaterialValue = new()
                    {
                        ConstitutiveModel = ConstitutiveModel.MohrCoulomb,
                        Cohesion = 1,
                        FrictionAngle = 125.123,
                        TensileStrength = 1,
                        Color = "#ffffff",
                        WaterSurface = WaterSurface.None,
                        Hu = Hu.Auto
                    },
                    PseudoStaticStaticMaterialValue = new()
                    {
                        ConstitutiveModel = ConstitutiveModel.MohrCoulomb,
                        Cohesion = 1,
                        FrictionAngle = 125.123,
                        TensileStrength = 1,
                        Color = "#ffffff",
                        WaterSurface = WaterSurface.None,
                        Hu = Hu.Auto
                    }
                }
            };
        }

        public static AddStaticMaterialRequest CreateUndrained()
        {
            return new()
            {
                RequestedBy = Guid.NewGuid(),
                RequestedBySuperSupport = true,
                StaticMaterial = new()
                {
                    Name = "U-01",
                    Structure = new() { Id = Guid.NewGuid() },
                    UndrainedStaticMaterialValue = new()
                    {
                        ConstitutiveModel = ConstitutiveModel.Undrained,
                        CohesionType = CohesionType.Constant,
                        Cohesion = 1,
                        TensileStrength = 1,
                        Color = "#ffffff",
                        WaterSurface = WaterSurface.None,
                        Hu = Hu.Auto
                    }
                }
            };
        }

        public static AddStaticMaterialRequest CreateNoStrenght()
        {
            return new()
            {
                RequestedBy = Guid.NewGuid(),
                RequestedBySuperSupport = true,
                StaticMaterial = new()
                {
                    Name = "NS-01",
                    Structure = new() { Id = Guid.NewGuid() },
                    PseudoStaticStaticMaterialValue = new()
                    {
                        ConstitutiveModel = ConstitutiveModel.NoStrength,
                        Color = "#ffffff",
                        WaterSurface = WaterSurface.None,
                        Hu = Hu.Auto
                    }
                }
            };
        }

        public static AddStaticMaterialRequest CreateInfiniteStrenght()
        {
            return new()
            {
                RequestedBy = Guid.NewGuid(),
                RequestedBySuperSupport = true,
                StaticMaterial = new()
                {
                    Name = "IS-01",
                    Structure = new() { Id = Guid.NewGuid() },
                    DrainedStaticMaterialValue = new()
                    {
                        ConstitutiveModel = ConstitutiveModel.InfiniteStrength,
                        Color = "#ffffff",
                        AllowSlidingAlongBoundary = true,
                        WaterSurface = WaterSurface.None,
                        Hu = Hu.Auto
                    }
                }
            };
        }

        public static AddStaticMaterialRequest CreateShearOrNormalFunction()
        {
            return new()
            {
                RequestedBy = Guid.NewGuid(),
                RequestedBySuperSupport = true,
                StaticMaterial = new()
                {
                    Name = "SONF-01",
                    Structure = new() { Id = Guid.NewGuid() },
                    UndrainedStaticMaterialValue = new()
                    {
                        ConstitutiveModel = ConstitutiveModel.ShearOrNormalFunction,
                        Color = "#ffffff",
                        PointValues = new()
                        {
                            new()
                            {
                                Index = 1,
                                Value1 = 30,
                                Value2 = 50
                            }
                        },
                        WaterSurface = WaterSurface.None,
                        Hu = Hu.Auto
                    }
                }
            };
        }

        public static AddStaticMaterialRequest CreateHoekBrown()
        {
            return new()
            {
                RequestedBy = Guid.NewGuid(),
                RequestedBySuperSupport = true,
                StaticMaterial = new()
                {
                    Name = "HB-01",
                    Structure = new() { Id = Guid.NewGuid() },
                    PseudoStaticStaticMaterialValue = new()
                    {
                        ConstitutiveModel = ConstitutiveModel.HoekBrown,
                        Color = "#ffffff",
                        UcsIntact = 15,
                        M = 16,
                        S = 1,
                        WaterSurface = WaterSurface.None,
                        Hu = Hu.Auto
                    }
                }
            };
        }

        public static AddStaticMaterialRequest CreateGeneralizedHoekBrown()
        {
            return new()
            {
                RequestedBy = Guid.NewGuid(),
                RequestedBySuperSupport = true,
                StaticMaterial = new()
                {
                    Name = "GHB-01",
                    Structure = new() { Id = Guid.NewGuid() },
                    DrainedStaticMaterialValue = new()
                    {
                        ConstitutiveModel = ConstitutiveModel.GeneralizedHoekBrown,
                        Color = "#ffffff",
                        UcsIntact = 15,
                        StrengthDefinition = StrengthDefinition.GsiMiD,
                        Gsi = 12,
                        Mi = 13,
                        D = 15,
                        WaterSurface = WaterSurface.None,
                        Hu = Hu.Auto
                    }
                }
            };
        }

        public static AddStaticMaterialRequest CreateVerticalStressRatio()
        {
            return new()
            {
                RequestedBy = Guid.NewGuid(),
                RequestedBySuperSupport = true,
                StaticMaterial = new()
                {
                    Name = "VSR-01",
                    Structure = new() { Id = Guid.NewGuid() },
                    DrainedStaticMaterialValue = new()
                    {
                        ConstitutiveModel = ConstitutiveModel.VerticalStressRatio,
                        Color = "#ffffff",
                        ResistanceRatio = 1.56,
                        MaximumShearStrength = 100,
                        MinimumShearStrength = 56,
                        TensileStrength = 5,
                        WaterSurface = WaterSurface.None,
                        Hu = Hu.Auto
                    },
                    PseudoStaticStaticMaterialValue = new()
                    {
                        ConstitutiveModel = ConstitutiveModel.VerticalStressRatio,
                        Color = "#ffffff",
                        ResistanceRatio = 1.56,
                        MaximumShearStrength = 100,
                        MinimumShearStrength = 56,
                        TensileStrength = 5,
                        WaterSurface = WaterSurface.None,
                        Hu = Hu.Auto
                    },
                    UndrainedStaticMaterialValue = new()
                    {
                        ConstitutiveModel = ConstitutiveModel.VerticalStressRatio,
                        Color = "#ffffff",
                        ResistanceRatio = 1.56,
                        MaximumShearStrength = 100,
                        MinimumShearStrength = 56,
                        TensileStrength = 5,
                        WaterSurface = WaterSurface.None,
                        Hu = Hu.Auto
                    }
                }
            };
        }

        public static AddStaticMaterialRequest CreateShansep()
        {
            return new()
            {
                RequestedBy = Guid.NewGuid(),
                RequestedBySuperSupport = true,
                StaticMaterial = new()
                {
                    Name = "S-01",
                    Structure = new() { Id = Guid.NewGuid() },
                    UndrainedStaticMaterialValue = new()
                    {
                        ConstitutiveModel = ConstitutiveModel.Shansep,
                        Color = "#ffffff",
                        A = 15,
                        S = 15,
                        M = 12,
                        StressHistoryType = StressHistoryType.OverconsolidationRatio,
                        StressHistoryMethod = StressHistoryMethod.ByElevation,
                        PointValues = new()
                    {
                        new()
                        {
                            Index = 1,
                            Value1 = 159,
                            Value2 = 1235
                        }
                    },
                        MaximumShearStrength = 12,
                        TensileStrength = 123,
                        WaterSurface = WaterSurface.None,
                        Hu = Hu.Auto
                    }
                }
            };
        }

        public static AddStaticMaterialRequest WithDatum(
            this AddStaticMaterialRequest request,
            double datum)
        {
            return request with
            {
                StaticMaterial = request.StaticMaterial with
                {
                    DrainedStaticMaterialValue = request.StaticMaterial
                            .DrainedStaticMaterialValue with
                        {
                            Datum = datum
                        }
                }
            };
        }
    }
}
