using Bogus;
using Model.Reading.GetById.Response;
using System;
using System.Linq;

namespace Application.Tests.Reading.Builders
{
    public static class GetReadingByIdResponseBuilder
    {
        public static GetReadingByIdResponse CreateDefault()
        {
            return new Faker<GetReadingByIdResponse>()
                .CustomInstantiator(faker => new GetReadingByIdResponse()
                {
                    Instrument = new Model._Shared.Instrument.Instrument(),
                    ReferenceReading = new GetReadingByIdResponse(),
                    Sections = Enumerable
                        .Range(0, 5)
                        .Select(i => new Model._Shared.Section.Section())
                        .ToList(),
                    Values = Enumerable
                        .Range(0, 5)
                        .Select(i => new ReadingValueResponse())
                        .ToList()
                });
        }

        public static GetReadingByIdResponse WithStructureId(
            this GetReadingByIdResponse response,
            Guid structureId)
        {
            response.Instrument.StructureId = structureId;
            return response;
        }
    }
}
