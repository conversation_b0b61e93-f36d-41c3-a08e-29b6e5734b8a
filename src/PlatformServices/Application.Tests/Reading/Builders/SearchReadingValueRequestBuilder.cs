using Model.Reading.Search.Request;
using System;

namespace Application.Tests.Reading.Builders
{
    public static class SearchReadingValueRequestBuilder
    {
        public static SearchReadingValueRequest CreateDefault()
        {
            return new()
            {
                RequestedBy = Guid.NewGuid(),
                RequestedBySuperSupport = true,
                RequestedUserRole = Domain.Enums.Role.SuperSupport,
                Body = new(),
                Query = new()
                {
                    ClientId = Guid.NewGuid(),
                    ClientUnitId = Guid.NewGuid(),
                    Page = 1,
                    PageSize = 15
                }
            };
        }

        public static SearchReadingValueRequest WithRequest(
            this SearchReadingValueRequest request,
            int page,
            int pageSize,
            Guid requestedBy
            )
        {
            request.Query.Page = page;
            request.Query.PageSize = pageSize;
            request.RequestedBy = requestedBy;

            return request;
        }
    }
}
