using FluentMigrator;

namespace Database.Migrations
{
    [Migration(************)]
    public class Migration************AddImageTables : Migration
    {
        public override void Up()
        {
            Create.Table("structure-images")
                .WithColumn("id").AsGuid().PrimaryKey("pk-structure-image")
                .WithColumn("imagefile-unique-name").AsString(255).Nullable()
                .WithColumn("imagefile-name").AsString(255).Nullable()
                .WithColumn("thumbnailfile-unique-name").AsString(255).Nullable()
                .WithColumn("thumbnailfile-name").AsString(255).Nullable()
                .WithColumn("structure-id").AsGuid().ForeignKey("structures", "id")
                .WithColumn("sizeinkb").AsDouble().Nullable()
                .WithColumn("description").AsString(50).Nullable()
                .WithColumn("created-by-user-id").AsGuid().NotNullable().ForeignKey("users", "id")
                .WithColumn("created-date").AsDateTime2().NotNullable()
                .WithDefaultValue(SystemMethods.CurrentUTCDateTime);

            Create.Table("instrument-images")
                .WithColumn("id").AsGuid().PrimaryKey("pk-instrument-image")
                .WithColumn("imagefile-unique-name").AsString(255).Nullable()
                .WithColumn("imagefile-name").AsString(255).Nullable()
                .WithColumn("thumbnailfile-unique-name").AsString(255).Nullable()
                .WithColumn("thumbnailfile-name").AsString(255).Nullable()
                .WithColumn("instrument-id").AsGuid().ForeignKey("instruments", "id")
                .WithColumn("sizeinkb").AsDouble().Nullable()
                .WithColumn("description").AsString(50).Nullable()
                .WithColumn("created-by-user-id").AsGuid().NotNullable().ForeignKey("users", "id")
                .WithColumn("created-date").AsDateTime2().NotNullable()
                .WithDefaultValue(SystemMethods.CurrentUTCDateTime);
        }

        public override void Down()
        {
            Delete.Table("structure-images");

            Delete.Table("instrument-images");
        }
    }
}
