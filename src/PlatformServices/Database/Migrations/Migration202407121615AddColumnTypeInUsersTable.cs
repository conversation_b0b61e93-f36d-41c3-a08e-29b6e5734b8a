using Domain.Enums;
using FluentMigrator;

namespace Database.Migrations
{
    [Migration(202407121615)]
    public class Migration202407121615AddColumnTypeInUsersTable : Migration
    {
        public override void Up()
        {
            Alter.Table("users")
                .AddColumn("type")
                .AsInt16()
                .NotNullable() 
                .WithDefaultValue((int)UserType.User);
        }

        public override void Down()
        {
            Delete.Column("type")
                .FromTable("users");
        }
    }
}
