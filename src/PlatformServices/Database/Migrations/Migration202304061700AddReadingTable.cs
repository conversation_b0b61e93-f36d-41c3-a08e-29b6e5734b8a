using FluentMigrator;

namespace Database.Migrations
{
    [Migration(************)]
    public class Migration************AddReadingTable : Migration
    {
        public override void Up()
        {
            Create.Table("readings")
                .WithColumn("id").AsGuid().PrimaryKey("pk-readings")
                .WithColumn("search-identifier").AsInt32().Identity()
                .WithColumn("instrument-id").AsGuid().ForeignKey("instruments", "id")
                .WithColumn("length-measurement-unit").AsInt16().Nullable()
                .WithColumn("pressure-measurement-unit").AsInt16().Nullable()
                .WithColumn("axis-measurement-unit").AsInt16().Nullable()
                .WithColumn("created-date").AsDateTime2().NotNullable()
                .WithDefaultValue(SystemMethods.CurrentUTCDateTime);
        }

        public override void Down()
        {
            Delete.Table("readings");
        }
    }
}
