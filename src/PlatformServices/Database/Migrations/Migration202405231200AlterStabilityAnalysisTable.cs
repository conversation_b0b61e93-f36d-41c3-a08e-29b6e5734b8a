using FluentMigrator;

namespace Database.Migrations
{
    [Migration(202405231200)]
    public class Migration202405231200AlterStabilityAnalysisTable : Migration
    {
        public override void Up()
        {
            Delete.Column("s01-file-name")
                .FromTable("safety-factors");

            Delete.Column("s01-file-unique-name")
                .FromTable("safety-factors");

            Delete.Column("slv-file-name")
                .FromTable("safety-factors");

            Delete.Column("slv-file-unique-name")
                .FromTable("safety-factors");
        }

        public override void Down()
        {
            Alter.Table("safety-factors")
                .AddColumn("s01-file-name").AsAnsiString(255).NotNullable().WithDefaultValue(string.Empty)
                .AddColumn("s01-file-unique-name").AsAnsiString(255).NotNullable().WithDefaultValue(string.Empty)
                .AddColumn("slv-file-name").AsAnsiString(255).NotNullable().WithDefaultValue(string.Empty)
                .AddColumn("slv-file-unique-name").AsAnsiString(255).NotNullable().WithDefaultValue(string.Empty);
        }
    }
}
