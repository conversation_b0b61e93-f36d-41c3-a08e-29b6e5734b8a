using FluentMigrator;
using System.Data;

namespace Database.Migrations
{
    [Migration(202402231100)]
    public class Migration202402231100AlterReadingSecurityLevelsTable : Migration
    {
        public override void Up()
        {
            Delete
                .ForeignKey("FK_reading-security-levels_reading-value-id_reading-values_id")
                .OnTable("reading-security-levels");

            Create.ForeignKey("FK_reading-security-levels_reading-value-id_reading-values_id")
                .FromTable("reading-security-levels").ForeignColumn("reading-value-id")
                .ToTable("reading-values").PrimaryColumn("id")
                .OnDeleteOrUpdate(Rule.Cascade);
        }

        public override void Down()
        {
            Delete.ForeignKey("FK_reading-security-levels_reading-value-id_reading-values_id")
                .OnTable("reading-security-levels");

            Create.ForeignKey("FK_reading-security-levels_reading-value-id_reading-values_id")
                .FromTable("reading-security-levels").ForeignColumn("reading-value-id")
                .ToTable("reading-values").PrimaryColumn("id");
        }
    }
}
