using FluentMigrator;

namespace Database.Migrations
{
    [Migration(************)]
    public class Migration************AddReadingSecurityLevelsTable : Migration
    {
        public override void Up()
        {
            Create.Table("reading-security-levels")
                .WithColumn("id").AsGuid().PrimaryKey("pk-reading-security-levels")
                .WithColumn("reading-value-id").AsGuid().NotNullable().ForeignKey("reading-values", "id")
                .WithColumn("attention").AsDouble().Nullable()
                .WithColumn("alert").AsDouble().Nullable()
                .WithColumn("emergency").AsDouble().Nullable()
                .WithColumn("abrupt-variation").AsDouble().Nullable()
                .WithColumn("maximum-daily-rainfall").AsDouble().Nullable()
                .WithColumn("rain-intensity").AsDouble().Nullable()
                .WithColumn("axis").AsInt16().Nullable();
        }

        public override void Down()
        {   
            Delete.Table("reading-security-levels");
        }
    }
}
