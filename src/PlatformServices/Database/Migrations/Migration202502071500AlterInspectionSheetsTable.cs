using FluentMigrator;

namespace Database.Migrations;

[Migration(202502071500)]
public class Migration202502071500AlterInspectionSheetsTable: Migration
{
    public override void Up()
    {
        Alter.Table("inspection-sheets") 
            .AddColumn("search_identifier_temp").AsInt32().Nullable();

        Execute.Sql("UPDATE [inspection-sheets] SET search_identifier_temp = [search-identifier];");

        Delete.Index("idx-inspection-sheets-search-identifier")
            .OnTable("inspection-sheets");
        
        Delete.Column("search-identifier").FromTable("inspection-sheets");

        Rename.Column("search_identifier_temp").OnTable("inspection-sheets").To("search-identifier");

        Alter.Table("inspection-sheets")
            .AlterColumn("search-identifier").AsInt32().NotNullable();
    }

    public override void Down()
    {
        Alter.Table("inspection-sheets")
            .AddColumn("search_identifier_temp").AsInt32().Nullable(); 

        Execute.Sql("UPDATE [inspection-sheets] SET search_identifier_temp = [search-identifier];");

        Delete.Column("search-identifier").FromTable("inspection-sheets");

        Alter.Table("inspection-sheets")
            .AddColumn("search-identifier").AsInt32().Identity().NotNullable();
        
        Create.Index("idx-inspection-sheets-search-identifier")
            .OnTable("inspection-sheets")
            .OnColumn("search-identifier");

        Execute.Sql("UPDATE [inspection-sheets] SET [search-identifier] = search_identifier_temp;");

        Delete.Column("search_identifier_temp").FromTable("inspection-sheets");
    }
}