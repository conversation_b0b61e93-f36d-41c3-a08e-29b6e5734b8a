using Database.Core;
using Model.Area.List.Response;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Database.Repositories.Area
{
    public interface IAreaRepository : IRepository<Domain.Entities.Area>
    {
        Task AddRangeAsync(IEnumerable<Domain.Entities.Area> areas);
        Task<bool> NameExists(string name, Guid id);
        Task<bool> NameExists(string name);
        Task<IEnumerable<ListAreaResponse>> ListAsync();
        Task<List<Domain.Entities.Area>> GetByIdsAsync(IEnumerable<Guid> ids);
    }
}
