using Database.Core.SqlBulk;

namespace Database.Repositories.Reading.Extensions;

public static class BulkColumnMapperExtensions
{
    public static BulkColumnMapper WithExistingReadingsQuery(
        this BulkColumnMapper mapper)
    {
        mapper.AddColumnMapping("Date", "date");
        mapper.AddColumnMapping("InstrumentId", "[instrument-id]");
        mapper.AddColumnMapping("MeasurementId", "[measurement-id]");
        mapper.AddColumnMapping("HasMeasurements", "[has-measurements]");
        
        return mapper;
    }

    public static BulkColumnMapper WithInsertReadingQuery(
        this BulkColumnMapper mapper)
    {
        mapper.AddColumnMapping("Id", "id");
        mapper.AddColumnMapping("InstrumentId", "[instrument-id]");
        mapper.AddColumnMapping("ReferenceReadingId", "[reference-reading-id]");
        mapper.AddColumnMapping("IsReferential", "[is-referential]");
        mapper.AddColumnMapping("InstrumentTopQuota", "[instrument-top-quota]");
        mapper.AddColumnMapping("InstrumentBaseQuota", "[instrument-base-quota]");
        mapper.AddColumnMapping("InstrumentAzimuth", "[instrument-azimuth]");
        mapper.AddColumnMapping("InstrumentMeasurementFrequency", "[instrument-measurement-frequency]");
        mapper.AddColumnMapping("InstrumentUpperLimit", "[instrument-upper-limit]");
        mapper.AddColumnMapping("InstrumentLowerLimit", "[instrument-lower-limit]");
        mapper.AddColumnMapping("IsAutomated", "[is-automated]");

        return mapper;
    }

    public static BulkColumnMapper WithInsertReadingValueQuery(
        this BulkColumnMapper mapper)
    {
        mapper.AddColumnMapping("Id", "id");
        mapper.AddColumnMapping("ReadingId", "[reading-id]");
        mapper.AddColumnMapping("MeasurementId", "[measurement-id]");
        mapper.AddColumnMapping("Date", "[date]");
        mapper.AddColumnMapping("Quota", "[quota]");
        mapper.AddColumnMapping("Depth", "[depth]");
        mapper.AddColumnMapping("Pressure", "[pressure]");
        mapper.AddColumnMapping("Dry", "[dry]");
        mapper.AddColumnMapping("PositiveA", "[positive-a]");
        mapper.AddColumnMapping("NegativeA", "[negative-a]");
        mapper.AddColumnMapping("PositiveB", "[positive-b]");
        mapper.AddColumnMapping("NegativeB", "[negative-b]");
        mapper.AddColumnMapping("AverageDisplacementA", "[average-displacement-a]");
        mapper.AddColumnMapping("AverageDisplacementB", "[average-displacement-b]");
        mapper.AddColumnMapping("AccumulatedDisplacementA", "[accumulated-displacement-a]");
        mapper.AddColumnMapping("AccumulatedDisplacementB", "[accumulated-displacement-b]");
        mapper.AddColumnMapping("DeviationA", "[deviation-a]");
        mapper.AddColumnMapping("DeviationB", "[deviation-b]");
        mapper.AddColumnMapping("AAxisReading", "[a-axis-reading]");
        mapper.AddColumnMapping("BAxisReading", "[b-axis-reading]");
        mapper.AddColumnMapping("Datum", "[datum]");
        mapper.AddColumnMapping("EastCoordinate", "[east-coordinate]");
        mapper.AddColumnMapping("NorthCoordinate", "[north-coordinate]");
        mapper.AddColumnMapping("EastDisplacement", "[east-displacement]");
        mapper.AddColumnMapping("NorthDisplacement", "[north-displacement]");
        mapper.AddColumnMapping("ZDisplacement", "[z-displacement]");
        mapper.AddColumnMapping("TotalPlanimetricDisplacement", "[total-planimetric-displacement]");
        mapper.AddColumnMapping("ADisplacement", "[a-displacement]");
        mapper.AddColumnMapping("BDisplacement", "[b-displacement]");
        mapper.AddColumnMapping("RelativeDepth", "[relative-depth]");
        mapper.AddColumnMapping("DeltaRef", "[delta-ref]");
        mapper.AddColumnMapping("AbsoluteSettlement", "[absolute-settlement]");
        mapper.AddColumnMapping("RelativeSettlement", "[relative-settlement]");
        mapper.AddColumnMapping("NatureId", "[nature-id]");
        mapper.AddColumnMapping("AAxisPga", "[a-axis-pga]");
        mapper.AddColumnMapping("BAxisPga", "[b-axis-pga]");
        mapper.AddColumnMapping("ZAxisPga", "[z-axis-pga]");
        mapper.AddColumnMapping("Pluviometry", "[pluviometry]");
        mapper.AddColumnMapping("Intensity", "[intensity]");
        mapper.AddColumnMapping("MeasurementQuota", "[measurement-quota]");
        mapper.AddColumnMapping("MeasurementDeltaRef", "[measurement-delta-ref]");
        mapper.AddColumnMapping("MeasurementLength", "[measurement-length]");
        mapper.AddColumnMapping("MeasurementIsReferential", "[measurement-is-referential]");

        return mapper;
    }

    public static BulkColumnMapper WithInsertReadingSecurityLevelsQuery(
        this BulkColumnMapper mapper)
    {
        mapper.AddColumnMapping("Id", "[id]");
        mapper.AddColumnMapping("ReadingValueId", "[reading-value-id]");
        mapper.AddColumnMapping("Attention", "[attention]");
        mapper.AddColumnMapping("Alert", "[alert]");
        mapper.AddColumnMapping("Emergency", "[emergency]");
        mapper.AddColumnMapping("AbruptVariation", "[abrupt-variation]");
        mapper.AddColumnMapping("MaximumDailyRainfall", "[maximum-daily-rainfall]");
        mapper.AddColumnMapping("RainIntensity", "[rain-intensity]");
        mapper.AddColumnMapping("Axis", "[axis]");
        
        return mapper;
    }

    public static BulkColumnMapper WithInsertReadingHistoryQuery(
        this BulkColumnMapper mapper)
    {
        mapper.AddColumnMapping("Id", "[id]");
        mapper.AddColumnMapping("ReadingId","[reading-id]");
        mapper.AddColumnMapping("Changes", "[changes]");
        mapper.AddColumnMapping("ModifiedByUserId", "[modified-by-user-id]");
        
        return mapper;
    }

    public static BulkColumnMapper WithInsertOutboxQuery(
        this BulkColumnMapper mapper)
    {
        mapper.AddColumnMapping("Id", "id");
        mapper.AddColumnMapping("Type", "type");
        mapper.AddColumnMapping("Data", "data");

        return mapper;
    }
}