using Database.Core;
using Model.Notification.Exists.Request;
using Model.Notification.GetNotificationsByUserId.Dto;
using Model.Notification.GetNotificationsByUserId.Request;
using Model.Notification.GetUserNotificationConfiguration.Request;
using Model.Notification.MarkAsRead.Request;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Database.Repositories.Notification
{
    public interface INotificationRepository : IRepository<Domain.Entities.Notification>
    {
        /// <summary>
        /// Delete notifications with read date less than or equal to this parameter
        /// </summary>
        /// <param name="fromYears">Parameter delimits notifications with read date less than or equal</param>
        /// <returns></returns>
        Task DeleteOldUserNotificationsAsync(int fromYears = 5);

        /// <summary>
        /// Delete notifications without users associated
        /// </summary>
        /// <param name="fromYears">Parameter delimits notifications with created date less than or equal</param>
        /// <returns></returns>
        Task DeleteNotificationsWithoutUsersAsync(int fromYears = 5);

        Task<IEnumerable<Domain.Entities.UserNotificationConfiguration>> GetUserNotificationConfigurationsAsync(GetUserNotificationConfigurationRequest request);
        Task UpsertUserNotificationConfigurationAsync(Domain.Entities.UserNotificationConfiguration configuration, Guid userId);
        Task MarkNotificationAsReadAsync(MarkNotificationAsReadRequest request);
        Task MarkAllNotificationsAsRead(Guid userId);
        Task<IEnumerable<NotificationDto>> GetNotificationsByUserIdAsync(GetNotificationsByUserIdRequest request);
        Task<int> CountNotificationsByUserIdAsync(GetNotificationsByUserIdRequest request);
        Task<bool> CheckIfExistsAsync(ExistsNotificationRequest request);
    }
}
