using MassTransit;
using Serilog;
using System;
using System.Threading.Tasks;

namespace Api.Configuration.MassTransit.Observers
{
    public class BusObserver : IBusObserver
    {
        public void CreateFaulted(Exception exception)
        {
            Log.Fatal("Bus creation fails", exception);
            throw exception;
        }

        public void PostCreate(IBus bus)
        {
            Log.Information("Bus has been created.");
        }

        public Task PostStart(IBus bus, Task<BusReady> busReady)
        {
            return Task.CompletedTask;
        }

        public Task PostStop(IBus bus)
        {
            Log.Information("Bus is stopped.");
            return Task.CompletedTask;
        }

        public Task PreStart(IBus bus)
        {
            return Task.CompletedTask;
        }

        public Task PreStop(IBus bus)
        {
            return Task.CompletedTask;
        }

        public Task StartFaulted(IBus bus, Exception exception)
        {
            Log.Fatal("Bus fails to start", exception);
            throw exception;
        }

        public Task StopFaulted(IBus bus, Exception exception)
        {
            Log.Fatal("Bus fails to stop", exception);
            throw exception;
        }
    }
}
