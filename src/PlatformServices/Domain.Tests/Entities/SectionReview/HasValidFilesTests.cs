using File = Domain.ValueObjects.File;

namespace Domain.Tests.Entities.SectionReview;

[<PERSON><PERSON><PERSON>("ConstructionStages", "HasValidFiles")]
public class HasValidFilesTests
{
    [Fact(DisplayName = "When files are valid, then returns true")]
    public void WhenFilesAreValidThenReturnsTrue()
    {
        var review = new Domain.Entities.SectionReview
        {
            Drawing = new File { UniqueName = "drawing.dxf" },
            Sli = new File { UniqueName = "sli.sli" }
        };

        var result = review.HasValidFiles();

        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When files are null, then returns false")]
    public void WhenFilesAreNullThenReturnsFalse()
    {
        var review = new Domain.Entities.SectionReview
        {
            Drawing = null,
            Sli = null
        };

        var result = review.HasValidFiles();

        result.Should().BeFalse();
    }
    
    [Theory(DisplayName = "When files are invalid, then returns false")]
    [InlineData(null)]
    [InlineData("")]
    public void WhenFilesAreInvalidThenReturnsFalse(string fileUniqueName)
    {
        var review = new Domain.Entities.SectionReview
        {
            Drawing = new File { UniqueName = fileUniqueName },
            Sli = new File { UniqueName = fileUniqueName }
        };

        var result = review.HasValidFiles();

        result.Should().BeFalse();
    }
}