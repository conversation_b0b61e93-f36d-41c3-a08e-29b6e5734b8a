using System;
using Model.Section._Shared.MapLineSetting;

namespace Application.Section.Extensions;

public static class MapLineSettingExtensions
{
    public static Domain.ValueObjects.MapLineSetting ConvertToValueObject(
        this MapLineSetting mapLineSetting)
    {
        ArgumentNullException.ThrowIfNull(mapLineSetting);

        return new Domain.ValueObjects.MapLineSetting()
        {
            Color = mapLineSetting.Color,
            Width = mapLineSetting.Width,
            Type = mapLineSetting.Type
        };
    }
}