using Application.Core;
using Application.Extensions;
using Dxf.Core.Extensions;
using IxMilia.Dxf;
using IxMilia.Dxf.Entities;
using Model.Section.TransformDxf.Request;
using Model.Section.TransformDxf.Response;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using static Application.Core.UseCaseResponseFactory<Model.Section.TransformDxf.Response.TransformDxfResponse>;

namespace Application.Section.TransformDxf
{
    public sealed class TransformDxfUseCase : ITransformDxfUseCase
    {
        private readonly TransformDxfRequestValidator _requestValidator = new();

        public async Task<UseCaseResponse<TransformDxfResponse>> Execute(TransformDxfRequest request)
        {
            try
            {
                if (request == null)
                {
                    return BadRequest(null);
                }

                var validationResult = await _requestValidator
                   .ValidateAsync(request);

                if (!validationResult.IsValid)
                {
                    return BadRequest(null, validationResult.Errors.ToErrorMessages());
                }

                using var stream = new System.IO.MemoryStream(Convert.FromBase64String(request.Base64));
                var dxfFile = DxfFile.Load(stream);

                dxfFile.Normalize();

                var hasEntities = dxfFile.Entities.Any();

                if (!hasEntities)
                {
                    return BadRequest(null, "000", "The DXF file is empty.");
                }

                var hasLayers = dxfFile.Layers.Any();

                if (!hasLayers)
                {
                    return BadRequest(null, "000", "The DXF file has no layers.");
                }

                foreach (var entity in dxfFile.Entities)
                {
                    var entityLayer = dxfFile.Layers.FirstOrDefault(l => l.Name == entity.Layer);

                    if (entityLayer == null)
                    {
                        continue;
                    }

                    if (entity.Color != DxfColor.ByLayer)
                    {
                        entityLayer.Color = entity.Color;
                    }
                }

                using var newStream = new System.IO.MemoryStream();
                dxfFile.SaveAs(newStream);

                return Ok(new TransformDxfResponse
                {
                    Base64 = Convert.ToBase64String(newStream.ToArray())
                });
            }
            catch (Exception e)
            {
                return InternalServerError(null, errors: e.ToErrorMessages("000"));
            }
        }
    }
}
