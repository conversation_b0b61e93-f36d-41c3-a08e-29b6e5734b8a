using Application.Core;
using Application.Extensions;
using Database.Repositories.Aspect;
using Model.Aspect.Add.Request;
using System;
using System.Threading.Tasks;
using Database.Repositories.Structure;
using Database.Repositories.User;
using Domain.Entities;
using static Application.Core.UseCaseResponseFactory<System.Guid>;

namespace Application.Aspect.Add;

public class AddAspectUseCase : IAddAspectUseCase
{
    private readonly IAspectRepository _aspectRepository;
    private readonly IUserRepository _userRepository;
    private readonly AddAspectRequestValidator _requestValidator = new();

    public AddAspectUseCase(
        IAspectRepository aspectRepository,
        IUserRepository userRepository)
    {
        _aspectRepository = aspectRepository;
        _userRepository = userRepository;
    }

    public async Task<UseCaseResponse<Guid>> Execute(AddAspectRequest request)
    {
        try
        {
            if (request == null)
            {
                return BadRequest(Guid.Empty);
            }

            await request.AddRequesterStructures(_userRepository);

            var validationResult = await _requestValidator
                .ValidateAsync(request);

            if (!validationResult.IsValid)
            {
                return BadRequest(Guid.Empty,
                    validationResult.Errors.ToErrorMessages());
            }

            var descriptionExists =
                await _aspectRepository.DescriptionExists(
                    request.Description);

            if (descriptionExists)
            {
                return BadRequest(Guid.Empty, "000",
                    "This description already exists.");
            }

            var aspect = Convert(request);

            await _aspectRepository.AddAsync(aspect);

            return Ok(aspect.Id);
        }
        catch (Exception e)
        {
            return InternalServerError(Guid.Empty,
                errors: e.ToErrorMessages("000"));
        }
    }

    private static Domain.Entities.Aspect Convert(AddAspectRequest request)
    {
        return new()
        {
            Id = request.Id ?? Guid.NewGuid(),
            Description = request.Description,
            AllowOptionNotApplicable = request.AllowOptionNotApplicable,
            ResponseForOccurrence = request.ResponseForOccurrence,
            Area = new()
            {
                Id = request.Area.Id
            }
        };
    }
}