using Domain.Entities;

namespace Application.Structure.Extensions;

public static class CircularParametersExtensions
{
    public static CircularParameters ConvertToEntity(
        this Model._Shared.Slide2Configuration.CircularParameters circularParameters)
    {
        return circularParameters != null
            ? new CircularParameters()
            {
                CalculationMethods = circularParameters.CalculationMethods,
                CircularSearchMethod = circularParameters.CircularSearchMethod,
                DivisionsAlongSlope = circularParameters.DivisionsAlongSlope,
                CirclesPerDivision = circularParameters.CirclesPerDivision,
                NumberOfIterations = circularParameters.NumberOfIterations,
                DivisionsNextIteration =
                    circularParameters.DivisionsNextIteration,
                RadiusIncrement = circularParameters.RadiusIncrement,
                NumberOfSurfaces = circularParameters.NumberOfSurfaces
            }
            : null;
    }
}