using Application.Core;
using Database.Repositories.User;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using static Application.Core.UseCaseResponseFactory<System.Collections.Generic.IEnumerable<System.Guid>>;

namespace Application.User.GetClients
{
    public sealed class GetUserClientsUseCase : IGetUserClientsUseCase
    {
        private readonly IUserRepository _userRepository;

        public GetUserClientsUseCase(IUserRepository userRepository)
        {
            _userRepository = userRepository;
        }

        public async Task<UseCaseResponse<IEnumerable<Guid>>> Execute(Guid userId)
        {
            try
            {
                if (userId == Guid.Empty)
                {
                    return BadRequest(null, "000", "User ID cannot be null.");
                }

                var clients = await _userRepository.GetClientIdsByUserIdAsync(userId);

                if (clients == null)
                {
                    return BadRequest(null, "000", "No clients were found for this user.");
                }

                return Ok(clients);
            }
            catch (Exception e)
            {
                return InternalServerError(null, errors: e.ToErrorMessages("000"));
            }
        }
    }
}
