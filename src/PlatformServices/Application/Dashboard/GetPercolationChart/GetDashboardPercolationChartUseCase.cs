using Application.Core;
using Application.Extensions;
using Database.Repositories.Instrument;
using Database.Repositories.Reading;
using Database.Repositories.User;
using Domain.Enums;
using Model.Chart.PercolationInstruments.Response;
using Model.Dashboard.GetPercolationChart.Request;
using System;
using System.Linq;
using System.Threading.Tasks;
using static Application.Core.UseCaseResponseFactory<Model.Chart.PercolationInstruments.Response.GetPercolationInstrumentDataResponse>;

namespace Application.Dashboard.GetPercolationChart
{
    public sealed class GetDashboardPercolationChartUseCase : IGetDashboardPercolationChartUseCase
    {
        private readonly IReadingRepository _readingRepository;
        private readonly IInstrumentRepository _instrumentRepository;
        private readonly IUserRepository _userRepository;
        private readonly GetDashboardPercolationChartRequestValidator _requestValidator = new();

        public GetDashboardPercolationChartUseCase(
            IReadingRepository readingRepository, 
            IInstrumentRepository instrumentRepository, 
            IUserRepository userRepository)
        {
            _readingRepository = readingRepository;
            _instrumentRepository = instrumentRepository;
            _userRepository = userRepository;
        }   

        public async Task<UseCaseResponse<GetPercolationInstrumentDataResponse>> Execute(GetDashboardPercolationChartRequest request)
        {
            try
            {
                if (request == null)
                {
                    return BadRequest(null);
                }

                await request.AddRequesterStructures(_userRepository);

                var validationResult = await _requestValidator
                   .ValidateAsync(request);

                if (!validationResult.IsValid)
                {
                    return BadRequest(null,
                        validationResult.Errors.ToErrorMessages());
                }

                var instruments = await _instrumentRepository.GetByTypeAndSectionAsync(new() 
                { 
                    InstrumentType.WaterLevelIndicator,
                    InstrumentType.OpenStandpipePiezometer,
                    InstrumentType.ElectricPiezometer,
                    InstrumentType.LinimetricRuler,
                    InstrumentType.Pluviograph,
                    InstrumentType.Pluviometer
                }, request.SectionId);  

                if (!request.RequestedBySuperSupport)
                {
                    foreach (var instrument in instruments)
                    {
                        if (!request.RequestedUserStructures.Any(x => x == instrument.Structure.Id))
                        {
                            return Forbidden(null, "000", $"User does not have permission to access instrument {instrument.Identifier} because it is not in the requested structures.");
                        }
                    }
                }

                var readings = await _readingRepository
                    .GetPercolationDataAsync(instruments.Select(x => x.Id).ToList(), request.StartDate, request.EndDate);

                if (readings == null || !readings.Any())
                {
                    return NoContent();
                }

                var groupedPercolationReadings = readings
                    .Where(x => x.Instrument.Type == InstrumentType.WaterLevelIndicator
                                || x.Instrument.Type == InstrumentType.ElectricPiezometer
                                || x.Instrument.Type == InstrumentType.OpenStandpipePiezometer)
                    .SelectMany(reading => reading.Values.Select(value => new
                    {
                        InstrumentId = reading.Instrument.Id,
                        InstrumentIdentifier = reading.Instrument.Identifier,
                        reading.InstrumentBaseQuota,
                        reading.InstrumentTopQuota,
                        MeasurementId = value.Measurement?.Id,
                        MeasurementIdentifier = value.Measurement?.Identifier,
                        value?.MeasurementQuota,
                        value.Date,
                        value.Quota,
                        value.Dry
                    }))
                    .GroupBy(
                        x => new { x.InstrumentIdentifier, x.MeasurementIdentifier },
                        x => new PercolationInstrumentReadingData
                        {
                            TopQuota = x.InstrumentTopQuota,
                            BaseQuota = x.MeasurementQuota ?? x.InstrumentBaseQuota,
                            Date = x.Date,
                            Quota = x.Quota,
                            Dry = (bool)x.Dry,
                        }
                    )
                    .Select(group => new PercolationInstrumentData()
                    {
                        InstrumentIdentifier = group.Key.InstrumentIdentifier.ToString(),
                        MeasurementIdentifier = group.Key.MeasurementIdentifier?.ToString(),
                        Readings = group.ToList()
                    }).ToList();

                var response = new GetPercolationInstrumentDataResponse()
                {
                    ClimateInstrumentReadings = readings
                        .Where(x => x.Instrument.Subtype == InstrumentSubtype.Climatic)
                        .Select(x => new
                        {
                            x.Values.FirstOrDefault()?.Date,
                            Pluviometry = x.Values.FirstOrDefault()?.Pluviometry ?? 0
                        })
                        .GroupBy(x => x.Date.HasValue ? x.Date.Value.Date : (DateTime?)null)
                        .Select(group => new ClimateInstrumentReadingData()
                        {
                            Date = group.Any() ? group.Max(x => x.Date ?? DateTime.MinValue) : DateTime.MinValue,
                            Pluviometry = group.Sum(x => x.Pluviometry)
                        })
                        .ToList(),
                    LinimetricRulersReadings = readings
                        .Where(x => x.Instrument.Type == InstrumentType.LinimetricRuler)
                        .Select(x => new LinimetricRulerReadingData()
                        {
                            Date = x.Values.FirstOrDefault()?.Date ?? DateTime.MinValue,
                            Quota = x.Values.FirstOrDefault()?.Quota,
                            Position = (LinimetricRulerPosition)x.Instrument.LinimetricRulerPosition
                        }).ToList(),
                    PercolationInstruments = groupedPercolationReadings
                };

                return Ok(response);
            }
            catch (Exception e)   
            {
                return InternalServerError(null, errors: e.ToErrorMessages("000"));
            }
        }
    }
}
