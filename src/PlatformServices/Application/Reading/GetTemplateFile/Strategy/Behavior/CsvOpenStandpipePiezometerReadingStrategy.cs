using System.Text;
using static Application.Reading.GetTemplateFile.TemplateHeaders.Names;

namespace Application.Reading.GetTemplateFile.Strategy.Behavior
{
    public class CsvOpenStandpipePiezometerReadingStrategy : IFileStrategy
    {
        public byte[] GetFile()
        {
            var stringBuilder = new StringBuilder();

            stringBuilder.AppendLine($"{InstrumentIdentifier},{DateAndTime},{Quota},{Depth},{Pressure},{Dry}");
            stringBuilder.AppendLine(",,,,,");

            return Encoding.UTF8.GetBytes(stringBuilder.ToString());
        }
    }
}
