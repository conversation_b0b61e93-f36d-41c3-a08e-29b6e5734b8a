using System.Text;
using static Application.Reading.GetTemplateFile.TemplateHeaders.Names;

namespace Application.Reading.GetTemplateFile.Strategy.Behavior
{
    public class CsvConventionalInclinometerReadingStrategy : IFileStrategy
    {
        public byte[] GetFile()
        {
            var stringBuilder = new StringBuilder();

            stringBuilder.AppendLine($"{InstrumentIdentifier},{MeasurementIdentifier},{Reference},{DateAndTime},{PositiveA},{NegativeA},{PositiveB},{NegativeB},{AverageDisplacementA},{AverageDisplacementB}");
            stringBuilder.AppendLine(",,,,,,,,,");

            return Encoding.UTF8.GetBytes(stringBuilder.ToString());
        }
    }
}
