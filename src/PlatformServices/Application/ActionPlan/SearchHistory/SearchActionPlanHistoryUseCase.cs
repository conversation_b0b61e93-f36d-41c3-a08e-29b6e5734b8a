using Application.Core;
using Application.Extensions;
using Database.Repositories.ActionPlan;
using Database.Repositories.User;
using Model.ActionPlan.SearchHistory.Request;
using Model.ActionPlan.SearchHistory.Response;
using Model.Core.Search.Pagination;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using static Application.Core.UseCaseResponseFactory<Model.Core.Search.Pagination.PaginationResponse>;

namespace Application.ActionPlan.SearchHistory
{
    public sealed class SearchActionPlanHistoryUseCase : ISearchActionPlanHistoryUseCase
    {
        private readonly IActionPlanRepository _actionPlanRepository;
        private readonly IUserRepository _userRepository;
        private readonly SearchActionPlanHistoryRequestValidator _requestValidator = new();

        public SearchActionPlanHistoryUseCase(
            IUserRepository userRepository, 
            IActionPlanRepository actionPlanRepository)
        {
            _userRepository = userRepository;
            _actionPlanRepository = actionPlanRepository;
        }

        public async Task<UseCaseResponse<PaginationResponse>> Execute(SearchActionPlanHistoryRequest request)
        {
            try
            {
                if (request == null)
                {
                    return BadRequest(null, "000", "Request cannot be null.");
                }

                await request.AddRequesterStructures(_userRepository);

                var validationResult = await _requestValidator
                    .ValidateAsync(request);

                if (!validationResult.IsValid)
                {
                    return BadRequest(null,
                        validationResult.Errors.ToErrorMessages());
                }

                var actionPlanHistory = await _actionPlanRepository
                    .SearchHistoryAsync(request);

                if (actionPlanHistory == null || !actionPlanHistory.Any())
                {
                    return NoContent();
                }

                var totalActionPlanHistory = await _actionPlanRepository
                    .CountHistoryAsync(request);

                return Ok(Map(actionPlanHistory, totalActionPlanHistory));
            }
            catch (Exception e)   
            {
                return InternalServerError(null, errors: e.ToErrorMessages("000"));
            }
        }

        private static PaginationResponse Map(IEnumerable<SearchActionPlanHistoryResponse> actionPlanHistory, int totalActionPlanHistory)
        {
            return new PaginationResponse
            {
                Data = actionPlanHistory,
                CurrentItemsCount = actionPlanHistory.Count(),
                TotalItemsCount = totalActionPlanHistory,
            };
        }
    }
}
