using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Application.Instrument.GetFile.Strategy.Behavior
{
    public class CsvPrismStrategy : IFileStrategy
    {
        const string HeaderLine = "Id," +
            "Estrutura," +
            "Identificador," +
            "Nome alternativo," +
            "Cota de topo," +
            "Azimute," +
            "Limite superior," +
            "Limite inferior," +
            "Datum," +
            "Lat," +
            "Lng," +
            "Les<PERSON>," +
            "Norte," +
            "Número da zona," +
            "Letra da zona," +
            "Automatizado," +
            "Online," +
            "Modelo," +
            "Responsável pela instalação," +
            "Data da instalação," +
            "Nível de atenção do deslocamento A," +
            "Nível de alerta do deslocamento A," +
            "Nível de emergência do deslocamento A," +
            "Variação brusca entre as leituras do deslocamento A," +
            "Nível de atenção do deslocamento B," +
            "Nível de alerta do deslocamento B," +
            "Nível de emergência do deslocamento B," +
            "Variação brusca entre as leituras do deslocamento B," +
            "Nível de atenção do deslocamento N," +
            "Nível de alerta do deslocamento N," +
            "Nível de emergência do deslocamento N," +
            "Variação brusca entre as leituras do deslocamento N," +
            "Nível de atenção do deslocamento E," +
            "Nível de alerta do deslocamento E," +
            "Nível de emergência do deslocamento E," +
            "Variação brusca entre as leituras do deslocamento E," +
            "Nível de atenção do deslocamento Planimétrico," +
            "Nível de alerta do deslocamento Planimétrico," +
            "Nível de emergência do deslocamento Planimétrico," +
            "Variação brusca entre as leituras do deslocamento Planimétrico," +
            "Nível de atenção do deslocamento Z," +
            "Nível de alerta do deslocamento Z," +
            "Nível de emergência do deslocamento Z," +
            "Variação brusca entre as leituras do deslocamento Z";

        public byte[] GetFile(List<Domain.Entities.Instrument> instruments)
        {
            var stringBuilder = new StringBuilder();

            stringBuilder.AppendLine(HeaderLine);

            foreach (var instrument in instruments)
            {
                stringBuilder.AppendLine(
                    $"{instrument.SearchIdentifier}," +
                    $"{instrument.Structure.SearchIdentifier}," +
                    $"{instrument.Identifier}," +
                    $"{instrument.AlternativeName}," +
                    $"{instrument.TopQuota}," +
                    $"{instrument.Azimuth}," +
                    $"{instrument.UpperLimit}," +
                    $"{instrument.LowerLimit}," +
                    $"{instrument.CoordinateSetting.Datum}," +
                    $"{instrument.CoordinateSetting.Systems.DecimalGeodetic.Latitude}," +
                    $"{instrument.CoordinateSetting.Systems.DecimalGeodetic.Longitude}," +
                    $"{instrument.CoordinateSetting.Systems.Utm.Easting}," +
                    $"{instrument.CoordinateSetting.Systems.Utm.Northing}," +
                    $"{instrument.CoordinateSetting.Systems.Utm.ZoneNumber}," +
                    $"{instrument.CoordinateSetting.Systems.Utm.ZoneLetter}," +
                    $"{instrument.Automated}," +
                    $"{instrument.Online}," +
                    $"{instrument.Model}," +
                    $"{instrument.ResponsibleForInstallation}," +
                    $"{instrument.InstallationDate}," +
                    $"{instrument.SecurityLevels?.FirstOrDefault(x => x.Axis == Domain.Enums.Axis.A)?.Attention}," +
                    $"{instrument.SecurityLevels?.FirstOrDefault(x => x.Axis == Domain.Enums.Axis.A)?.Alert}," +
                    $"{instrument.SecurityLevels?.FirstOrDefault(x => x.Axis == Domain.Enums.Axis.A)?.Emergency}," +
                    $"{instrument.SecurityLevels?.FirstOrDefault(x => x.Axis == Domain.Enums.Axis.A)?.AbruptVariation}," +
                    $"{instrument.SecurityLevels?.FirstOrDefault(x => x.Axis == Domain.Enums.Axis.B)?.Attention}," +
                    $"{instrument.SecurityLevels?.FirstOrDefault(x => x.Axis == Domain.Enums.Axis.B)?.Alert}," +
                    $"{instrument.SecurityLevels?.FirstOrDefault(x => x.Axis == Domain.Enums.Axis.B)?.Emergency}," +
                    $"{instrument.SecurityLevels?.FirstOrDefault(x => x.Axis == Domain.Enums.Axis.B)?.AbruptVariation}," +
                    $"{instrument.SecurityLevels?.FirstOrDefault(x => x.Axis == Domain.Enums.Axis.N)?.Attention}," +
                    $"{instrument.SecurityLevels?.FirstOrDefault(x => x.Axis == Domain.Enums.Axis.N)?.Alert}," +
                    $"{instrument.SecurityLevels?.FirstOrDefault(x => x.Axis == Domain.Enums.Axis.N)?.Emergency}," +
                    $"{instrument.SecurityLevels?.FirstOrDefault(x => x.Axis == Domain.Enums.Axis.N)?.AbruptVariation}," + 
                    $"{instrument.SecurityLevels?.FirstOrDefault(x => x.Axis == Domain.Enums.Axis.E)?.Attention}," +
                    $"{instrument.SecurityLevels?.FirstOrDefault(x => x.Axis == Domain.Enums.Axis.E)?.Alert}," +
                    $"{instrument.SecurityLevels?.FirstOrDefault(x => x.Axis == Domain.Enums.Axis.E)?.Emergency}," +
                    $"{instrument.SecurityLevels?.FirstOrDefault(x => x.Axis == Domain.Enums.Axis.E)?.AbruptVariation}," +     
                    $"{instrument.SecurityLevels?.FirstOrDefault(x => x.Axis == Domain.Enums.Axis.Planimetric)?.Attention}," +
                    $"{instrument.SecurityLevels?.FirstOrDefault(x => x.Axis == Domain.Enums.Axis.Planimetric)?.Alert}," +
                    $"{instrument.SecurityLevels?.FirstOrDefault(x => x.Axis == Domain.Enums.Axis.Planimetric)?.Emergency}," +
                    $"{instrument.SecurityLevels?.FirstOrDefault(x => x.Axis == Domain.Enums.Axis.Planimetric)?.AbruptVariation}," +
                    $"{instrument.SecurityLevels?.FirstOrDefault(x => x.Axis == Domain.Enums.Axis.Z)?.Attention}," +
                    $"{instrument.SecurityLevels?.FirstOrDefault(x => x.Axis == Domain.Enums.Axis.Z)?.Alert}," +
                    $"{instrument.SecurityLevels?.FirstOrDefault(x => x.Axis == Domain.Enums.Axis.Z)?.Emergency}," +
                    $"{instrument.SecurityLevels?.FirstOrDefault(x => x.Axis == Domain.Enums.Axis.Z)?.AbruptVariation}");
            }

            return Encoding.UTF8.GetBytes(stringBuilder.ToString());
        }
    }
}
