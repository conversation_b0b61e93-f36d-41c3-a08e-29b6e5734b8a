using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Application.Instrument.GetFile.Strategy.Behavior
{
    public class CsvInclinometersStrategy : IFileStrategy
    {
        const string HeaderLine = "Id," +
            "Estrutura," +
            "Identificador," +
            "Nome alternativo," +
            "Cota de topo," +
            "Azimute," +
            "Datum," +
            "Lat," +
            "Lng," +
            "<PERSON><PERSON>," +
            "Norte," +
            "Número da zona," +
            "Letra da zona," +
            "Automatizado," +
            "Online," +
            "Modelo," +
            "Responsável pela instalação," +
            "Data da instalação," +
            "Identificador do ponto de medição," +
            "Nome alternativo do ponto de medição," +
            "Ativo," +
            "Realocação," +
            "Comprimento," +
            "Profundidade do ponto de medição," +
            "Cota do ponto de medição," +
            "Litotipo," +
            "Nível de atenção," +
            "Nível de alerta," +
            "Nível de emergência," +
            "Variação abrupta entre as leituras";

        public byte[] GetFile(List<Domain.Entities.Instrument> instruments)
        {
            var stringBuilder = new StringBuilder();

            stringBuilder.AppendLine(HeaderLine);

            foreach (var instrument in instruments)
            {
                instrument.Measurements = instrument.Measurements.OrderBy(m => m.Quota).ToList();

                foreach (var measurement in instrument.Measurements)
                {
                    stringBuilder.AppendLine(
                        $"{instrument.SearchIdentifier}," +
                        $"{instrument.Structure.SearchIdentifier}," +
                        $"{instrument.Identifier}," +
                        $"{instrument.AlternativeName}," +
                        $"{instrument.TopQuota}," +
                        $"{instrument.Azimuth}," +
                        $"{instrument.CoordinateSetting.Datum}," +
                        $"{instrument.CoordinateSetting.Systems.DecimalGeodetic.Latitude}," +
                        $"{instrument.CoordinateSetting.Systems.DecimalGeodetic.Longitude}," +
                        $"{instrument.CoordinateSetting.Systems.Utm.Easting}," +
                        $"{instrument.CoordinateSetting.Systems.Utm.Northing}," +
                        $"{instrument.CoordinateSetting.Systems.Utm.ZoneNumber}," +
                        $"{instrument.CoordinateSetting.Systems.Utm.ZoneLetter}," +
                        $"{instrument.Automated}," +
                        $"{instrument.Online}," +
                        $"{instrument.Model}," +
                        $"{instrument.ResponsibleForInstallation}," +
                        $"{instrument.InstallationDate}," +
                        $"{measurement.Identifier}," +
                        $"{measurement.AlternativeName}," +
                        $"{measurement.Active}," +
                        $"{false}," +
                        $"{measurement.Length}," +
                        $"{measurement.Depth}," +
                        $"{measurement.Quota}," +
                        $"{measurement.Lithotype}," +
                        $"{measurement.SecurityLevels?.Attention}," +
                        $"{measurement.SecurityLevels?.Alert}," +
                        $"{measurement.SecurityLevels?.Emergency}," +
                        $"{measurement.SecurityLevels?.AbruptVariation}");
                }
            }

            return Encoding.UTF8.GetBytes(stringBuilder.ToString());
        }
    }
}
