using KellermanSoftware.CompareNetObjects;
using KellermanSoftware.CompareNetObjects.TypeComparers;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Application.Extensions.Comparer
{
    /// <summary>
    /// Class to compare changes made to the list of client units in the users entity
    /// </summary>
    public class ClientUnitListComparer : BaseTypeComparer
    {
        private bool _typeHasBeenCompared = false;

        public ClientUnitListComparer(RootComparer rootComparer) : base(rootComparer)
        {
        }

        public override bool IsTypeMatch(Type type1, Type type2)
        {
            return type1 == typeof(List<Domain.Entities.ClientUnit>);
        }

        public override void CompareType(CompareParms parms)
        {
            if (!_typeHasBeenCompared)
            {
                var obj1 = (List<Domain.Entities.ClientUnit>)parms.Object1;
                var obj2 = (List<Domain.Entities.ClientUnit>)parms.Object2;

                if (obj1.Any(x => !obj2.Contains(x)) || obj2.Any(x => !obj1.Contains(x)))
                {
                    _typeHasBeenCompared = true;
                    AddDifference(parms);
                }
            }
        }
    }
}
