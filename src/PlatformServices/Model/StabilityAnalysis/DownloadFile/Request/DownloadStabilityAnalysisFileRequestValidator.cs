using FluentValidation;

namespace Model.StabilityAnalysis.DownloadFile.Request
{
    public sealed class DownloadStabilityAnalysisFileRequestValidator : AbstractValidator<DownloadStabilityAnalysisFileRequest>
    {
        public DownloadStabilityAnalysisFileRequestValidator()
        {
            RuleFor(request => request.StabilityAnalysisId)
                .NotEmpty();

            RuleFor(request => request.SafetyFactorId)
                .NotEmpty();
        }
    }
}
