using FluentValidation;
using System;
using System.Linq;

namespace Model.Simulation.Patch.Request
{
    public sealed class PatchSimulationRequestValidator : AbstractValidator<PatchSimulationRequest>
    {
        public PatchSimulationRequestValidator()
        {
            RuleFor(x => x.Id).NotEmpty();

            RuleFor(x => x.Sections).NotEmpty();

            RuleForEach(x => x.Sections)
                .Must(x => x.SectionId != Guid.Empty);
        }
    }
}
