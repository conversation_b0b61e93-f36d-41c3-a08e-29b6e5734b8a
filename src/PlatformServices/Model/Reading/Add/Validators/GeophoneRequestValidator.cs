using FluentValidation;
using Model._Shared.Nature;
using Model.Reading._Shared;
using Model.Reading._Shared.ReadingValue;

namespace Model.Reading.Add.Validators
{
    public class GeophoneRequestValidator : AbstractValidator<ReadingValueRequest>
    {
        private readonly NatureValidator _natureValidator = new();

        public GeophoneRequestValidator(ReadingRequest reading)
        {
            CascadeMode = CascadeMode.Stop;

            RuleFor(x => x.Nature)
                .NotNull()
                .SetValidator(_natureValidator);

            RuleFor(x => x.AAxisPga)
                .NotNull();

            RuleFor(x => x.BAxisPga)
                .NotNull();

            RuleFor(x => x.ZAxisPga)
                .NotNull();

            RuleFor(x => x.EastCoordinate)
                .NotNull()
                .GreaterThanOrEqualTo(0);

            RuleFor(x => x.NorthCoordinate)
                .NotNull()
                .GreaterThanOrEqualTo(0);

            RuleFor(x => x.Measurement)
                .Null();

            RuleFor(x => x.Quota)
                .Null();

            RuleFor(x => x.Dry)
                .Null();

            RuleFor(x => x.Pressure)
                .Null();

            RuleFor(x => x.Depth)
                .Null();

            RuleFor(x => reading.IsReferential)
                .Null();

            RuleFor(x => x.PositiveA)
                .Null();

            RuleFor(x => x.NegativeA)
                .Null();

            RuleFor(x => x.PositiveB)
                .Null();

            RuleFor(x => x.NegativeB)
                .Null();

            RuleFor(x => x.AverageDisplacementA)
                .Null();

            RuleFor(x => x.AverageDisplacementB)
                .Null();

            RuleFor(x => x.AccumulatedDisplacementA)
                .Null();

            RuleFor(x => x.AccumulatedDisplacementB)
                .Null();

            RuleFor(x => x.DeviationA)
                .Null();

            RuleFor(x => x.DeviationB)
                .Null();

            RuleFor(x => x.AAxisReading)
                .Null();

            RuleFor(x => x.BAxisReading)
                .Null();

            RuleFor(x => x.Datum)
                .Null();

            RuleFor(x => x.EastDisplacement)
                .Null();

            RuleFor(x => x.NorthDisplacement)
                .Null();

            RuleFor(x => x.ZDisplacement)
                .Null();

            RuleFor(x => x.TotalPlanimetricDisplacement)
                .Null();

            RuleFor(x => x.ADisplacement)
                .Null();

            RuleFor(x => x.BDisplacement)
                .Null();

            RuleFor(x => x.RelativeDepth)
                .Null();

            RuleFor(x => x.DeltaRef)
                .Null();

            RuleFor(x => x.AbsoluteSettlement)
                .Null();

            RuleFor(x => x.RelativeSettlement)
                .Null();

            RuleFor(x => x.Intensity)
                .Null();

            RuleFor(x => x.Pluviometry)
                .Null();
        }
    }
}
