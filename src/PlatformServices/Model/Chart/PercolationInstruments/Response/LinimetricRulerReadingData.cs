using Domain.Enums;
using System;
using System.Text.Json.Serialization;

namespace Model.Chart.PercolationInstruments.Response
{
    public sealed record LinimetricRulerReadingData
    {
        [JsonPropertyName("date")]
        public DateTime Date { get; set; }

        [JsonPropertyName("quota")]
        public decimal? Quota { get; set; }

        [JsonPropertyName("linimetric_ruler_position")]
        public LinimetricRulerPosition Position { get; set; }
    }
}
