using FluentValidation;

namespace Model.StaticMaterial._Shared.Validators
{
    public class HoekBrownRequestValidator : AbstractValidator<StaticMaterialValueModel>
    {
        public HoekBrownRequestValidator()
        {
            CascadeMode = CascadeMode.Stop;

            RuleFor(x => x.Cohesion)
                .Null();

            RuleFor(x => x.FrictionAngle)
                .Null();

            RuleFor(x => x.TensileStrength)
                .Null();

            RuleFor(x => x.CohesionType)
                .Null();

            RuleFor(x => x.CohesionVariation)
                .Null();

            RuleFor(x => x.Datum)
                .Null();

            RuleFor(x => x.AllowSlidingAlongBoundary)
                .Null();

            RuleFor(x => x.UcsIntact)
                .NotNull()
                .GreaterThanOrEqualTo(0);

            RuleFor(x => x.M)
                .NotNull()
                .GreaterThanOrEqualTo(0);

            RuleFor(x => x.D)
                .Null();

            RuleFor(x => x.S)
                .NotNull()
                .GreaterThanOrEqualTo(0);

            RuleFor(x => x.StrengthDefinition)
                .Null();

            RuleFor(x => x.Gsi)
                .Null();

            RuleFor(x => x.Mi)
                .Null();

            RuleFor(x => x.A)
                .Null();

            RuleFor(x => x.Mb)
                .Null();

            RuleFor(x => x.ResistanceRatio)
                .Null();

            RuleFor(x => x.MaximumShearStrength)
                .Null();

            RuleFor(x => x.MinimumShearStrength)
                .Null();

            RuleFor(x => x.StressHistoryMethod)
                .Null();

            RuleFor(x => x.StressHistoryType)
                .Null();

            RuleFor(x => x.Constant)
                .Null();

            RuleFor(x => x.Maximum)
                .Null();

            RuleFor(x => x.PointValues)
                .Empty();
        }
    }
}
