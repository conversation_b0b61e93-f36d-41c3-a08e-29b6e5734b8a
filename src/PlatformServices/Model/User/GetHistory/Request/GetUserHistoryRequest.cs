using Model.Core.Search.Pagination;
using System;
using System.Text.Json.Serialization;

namespace Model.User.GetHistory.Request
{
    public sealed record GetUserHistoryRequest : RequestWithMetadata, IPaginationRequest
    {
        [JsonIgnore]
        public Guid UserId { get; set; }
        public int Page { get; set; }
        public int PageSize { get; set; }

        private int Skip
        {
            get
            {
                return (Page - 1) * PageSize;
            }
        }

        public int GetSkip() => Skip;
    }
}
