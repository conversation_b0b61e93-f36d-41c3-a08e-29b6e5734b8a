using Coordinate.Core.Classes;
using Domain.Enums;
using Model._Shared.Orientation;
using Model._Shared.Section;
using Model._Shared.Slide2Configuration;
using Model.Structure._Shared.AspectStructure;
using Model.Structure._Shared.Dimension;
using Model.Structure._Shared.EnvironmentalDamagePotential;
using Model.Structure._Shared.Layer;
using Model.Structure._Shared.Region;
using Model.StructureType.GetById.Response;
using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace Model.Structure.GetById.Response
{
    public sealed record GetStructureByIdResponse
    {
        [JsonPropertyName("id")]
        public Guid Id { get; set; }

        [JsonPropertyName("client_unit")]
        public Model._Shared.ClientUnit.ClientUnit ClientUnit { get; set; }

        [JsonPropertyName("client")]
        public Model._Shared.Client.Client Client { get; set; }

        [JsonPropertyName("name")]
        public string Name { get; set; }

        [JsonPropertyName("status")]
        public StructureStatus Status { get; set; }

        [JsonPropertyName("protocol")]
        public Protocol? Protocol { get; set; }

        [JsonPropertyName("coordinate_setting")]
        public CoordinateSetting CoordinateSetting { get; set; }

        [JsonPropertyName("structure_type")]
        public GetStructureTypeByIdResponse StructureType { get; set; }

        [JsonPropertyName("should_evaluate_drained_condition")]
        public bool ShouldEvaluateDrainedCondition { get; set; }

        [JsonPropertyName("should_evaluate_undrained_condition")]
        public bool ShouldEvaluateUndrainedCondition { get; set; }

        [JsonPropertyName("should_evaluate_pseudo_static_condition")]
        public bool ShouldEvaluatePseudoStaticCondition { get; set; }

        [JsonPropertyName("map_configuration")]
        public _Shared.MapConfiguration.MapConfiguration MapConfiguration { get; set; }

        [JsonPropertyName("slide2_configuration")]
        public Slide2Configuration Slide2Configuration { get; set; }

        [JsonPropertyName("has_auto_update")]
        public bool HasAutoUpdate { get; set; }

        [JsonPropertyName("frequency_to_fetch_data")]
        public _Shared.AutomationFrequency.AutomationFrequency FrequencyToFetchData { get; set; }

        [JsonPropertyName("frequency_to_generate_packages")]
        public _Shared.AutomationFrequency.AutomationFrequency FrequencyToGeneratePackages { get; set; }

        [JsonPropertyName("seismic_coefficient")]
        public Orientation SeismicCoefficient { get; set; }

        [JsonPropertyName("gravity")]
        public double Gravity { get; set; }

        [JsonPropertyName("country")]
        public Region Country { get; set; }

        [JsonPropertyName("state")]
        public Region State { get; set; }

        [JsonPropertyName("city")]
        public Region City { get; set; }

        [JsonPropertyName("purpose")]
        public string Purpose { get; set; }

        [JsonPropertyName("construction_stages")]
        public int? ConstructionStages { get; set; }

        [JsonPropertyName("crest_dimension")]
        public Dimension CrestDimension { get; set; }

        [JsonPropertyName("total_height")]
        public double? TotalHeight { get; set; }

        [JsonPropertyName("downstream_slope")]
        public string DownstreamSlope { get; set; }

        [JsonPropertyName("upstream_slope")]
        public string UpstreamSlope { get; set; }

        [JsonPropertyName("classification")]
        public Classification? Classification { get; set; }

        [JsonPropertyName("section_type")]
        public string SectionType { get; set; }

        [JsonPropertyName("foundation_type")]
        public string FoundationType { get; set; }

        [JsonPropertyName("raising_method")]
        public string RaisingMethod { get; set; }

        [JsonPropertyName("expected_elevations")]
        public int? ExpectedElevations { get; set; }

        [JsonPropertyName("elevations_made")]
        public int? ElevationsMade { get; set; }

        [JsonPropertyName("reservoir_design_volume")]
        public double? ReservoirDesignVolume { get; set; }

        [JsonPropertyName("current_reservoir_volume")]
        public double? CurrentReservoirVolume { get; set; }

        [JsonPropertyName("internal_drainage")]
        public string InternalDrainage { get; set; }

        [JsonPropertyName("superficial_drainage")]
        public string SuperficialDrainage { get; set; }

        [JsonPropertyName("basin_area_in_square_kilometers")]
        public double? BasinAreaInSquareKilometers { get; set; }

        [JsonPropertyName("project_precipitation")]
        public double? ProjectPrecipitation { get; set; }

        [JsonPropertyName("full_of_project")]
        public int? FullOfProject { get; set; }

        [JsonPropertyName("maximum_influent_flow")]
        public double? MaximumInfluentFlow { get; set; }

        [JsonPropertyName("project_flow")]
        public double? ProjectFlow { get; set; }

        [JsonPropertyName("normal_maximum_water_level")]
        public double? NormalMaximumWaterLevel { get; set; }

        [JsonPropertyName("maximum_water_level_maximorum")]
        public double? MaximumWaterLevelMaximorum { get; set; }

        [JsonPropertyName("freeboard_normal_maximum_water_level")]
        public double? FreeboardNormalMaximumWaterLevel { get; set; }

        [JsonPropertyName("freeboard_maximum_water_level_maximorum")]
        public double? FreeboardMaximumWaterLevelMaximorum { get; set; }

        [JsonPropertyName("spillway")]
        public string Spillway { get; set; }

        [JsonPropertyName("aspects")]
        public List<AspectStructure> Aspects { get; set; }

        [JsonPropertyName("responsibles")]
        public List<Model._Shared.Responsible.Responsible> Responsibles { get; set; }

        [JsonPropertyName("construction_year")]
        public int? ConstructionYear { get; set; }

        [JsonPropertyName("start_of_operation_date")]
        public int? StartOfOperationDate { get; set; }

        [JsonPropertyName("end_of_operation_date")]
        public int? EndOfOperationDate { get; set; }

        [JsonPropertyName("designing_company")]
        public string DesigningCompany { get; set; }

        [JsonPropertyName("current_status_of_dam")]
        public string CurrentStatus { get; set; }

        [JsonPropertyName("crest_quota")]
        public decimal? CrestQuota { get; set; }

        [JsonPropertyName("spillway_sill_quota")]
        public decimal? SpillwaySillQuota { get; set; }

        [JsonPropertyName("intercepted_watercourse")]
        public string InterceptedWatercourse { get; set; }

        [JsonPropertyName("environmental_damage_potential")]
        public EnvironmentalDamagePotential EnvironmentalDamagePotential { get; set; }

        [JsonPropertyName("layers")]
        public List<Layer> Layers { get; set; }
    }
}
