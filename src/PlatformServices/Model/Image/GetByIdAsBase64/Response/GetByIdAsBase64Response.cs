using System;
using System.Text.Json.Serialization;

namespace Model.Image.GetByIdAsBase64.Response
{
    public class GetByIdAsBase64Response
    {
        [JsonPropertyName("base64")]
        public string Base64 { get; set; }
        [JsonPropertyName("uploaded_by")]
        public string UploadedBy { get; set; }
        [JsonPropertyName("uploaded_date")]
        public DateTime UploadedDate { get; set; }
        [JsonPropertyName("description")]
        public string Description { get; set; }
        [JsonPropertyName("original_file_name")]
        public string OriginalFileName { get; set; }
    }
}
