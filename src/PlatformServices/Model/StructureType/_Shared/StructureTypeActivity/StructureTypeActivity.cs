using Domain.Enums;
using System;
using System.Text.Json.Serialization;

namespace Model.StructureType._Shared.StructureTypeActivity
{
    public sealed record StructureTypeActivity
    {
        [JsonPropertyName("id")]
        public Guid? Id { get; set; }

        [JsonPropertyName("activity")]
        public Activity Activity { get; set; }

        [JsonPropertyName("index")]
        public int Index { get; set; }
    }
}
