using FluentValidation;
using Model.Core.Search.Pagination;

namespace Model.StructureType.Search.Request
{
    public class SearchStructureTypeRequestValidator : AbstractValidator<SearchStructureTypeRequest>
    {
        private readonly PaginationRequestValidator _paginationValidator = new();

        public SearchStructureTypeRequestValidator()
        {
            CascadeMode = CascadeMode.Stop;

            RuleFor(x => new PaginationRequest { Page = x.Page, PageSize = x.PageSize })
                .NotEmpty()
                .SetValidator(_paginationValidator);

            RuleFor(x => x.Name)
                .Length(1, 32)
                .When(x => !string.IsNullOrEmpty(x.Name));

            RuleFor(x => x.SearchIdentifier)
                .GreaterThanOrEqualTo(1)
                .When(x => x.SearchIdentifier != null);
        }
    }
}
