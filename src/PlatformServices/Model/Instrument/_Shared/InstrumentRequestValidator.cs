using Coordinate.Core.Model._Shared;
using FluentValidation;
using Model._Shared.Structure;
using System.Linq;

namespace Model.Instrument._Shared
{
    public class InstrumentRequestValidator : AbstractValidator<InstrumentRequest>
    {
        private readonly StructureRequestValidator _structureValidator = new();
        private readonly CoordinateSettingRequestValidator _coordinateValidator = new();

        public InstrumentRequestValidator()
        {
            CascadeMode = CascadeMode.Stop;

            RuleFor(x => x.Identifier)
                .NotEmpty()
                .MaximumLength(32);

            RuleFor(x => x.AlternativeName)
                .MaximumLength(32)
                .When(x => !string.IsNullOrEmpty(x.Identifier));

            RuleFor(x => x.ResponsibleForInstallation)
                .MaximumLength(255)
                .When(x => !string.IsNullOrEmpty(x.ResponsibleForInstallation));

            RuleFor(x => x.Model)
                .MaximumLength(255)
                .When(x => !string.IsNullOrEmpty(x.Model));

            RuleFor(x => x.Azimuth)
                .InclusiveBetween(0, 360)
                .When(x => x.Azimuth != null)
                .WithMessage(x => $"Instrument '{x.Identifier}' must have an azimuth between 0 and 360.");

            RuleFor(x => x.Structure)
                .NotEmpty()
                .SetValidator(_structureValidator)
                .WithMessage(x => $"Instrument structure '{x.Identifier}' is not valid.");

            RuleFor(x => x.Type)
                .IsInEnum()
                .WithMessage(x => $"Instrument type '{x.Identifier}' is not valid.");

            RuleFor(x => x.CoordinateSetting)
                .NotNull()
                .SetValidator(_coordinateValidator);

            RuleFor(x => x.InstallationDate)
                .NotEmpty();

            RuleFor(x => x)
                .SetValidator(x => InstrumentValidatorFactory.GetValidator(x.Type));

            RuleFor(x => x)
                .Must(x => x.Measurements.GroupBy(y => y.Identifier?.ToLower()).Count() == x.Measurements.Count)
                .WithMessage(x => $"Instrument '{x.Identifier}' has duplicate measurements.")
                .When(x => x.Measurements?.Count > 0);
        }
    }
}
