using Domain.Enums;
using FluentValidation;
using Model.Extensions;
using System.Linq;

namespace Model.Instrument._Shared.Validators
{
    public class PrismRequestValidator : AbstractValidator<InstrumentRequest>
    {
        public PrismRequestValidator()
        {
            CascadeMode = CascadeMode.Stop;

            RuleFor(x => x.BaseQuota)
                .Null()
                .WithMessage(x => $"Instrument '{x.Identifier}' cannot have a base quota.");

            RuleFor(x => x.UpperLimit)
                .NotNull()
                .GreaterThanOrEqualTo(0)
                .WithMessage(x => $"Instrument '{x.Identifier}' must have an upper limit.");

            RuleFor(x => x.LowerLimit)
                .NotNull()
                .GreaterThanOrEqualTo(0)
                .WithMessage(x => $"Instrument '{x.Identifier}' must have a lower limit.");

            RuleFor(x => x.Depth)
                .Null()
                .WithMessage(x => $"Instrument '{x.Identifier}' cannot have a depth.");

            RuleFor(x => x.GeophoneType)
                .Null()
                .WithMessage(x => $"Instrument '{x.Identifier}' cannot have a geophone type.");

            RuleFor(x => x.Elevation)
                .Null()
                .WithMessage(x => $"Instrument '{x.Identifier}' cannot have an elevation.");

            RuleFor(x => x.TopQuota)
                .NotNull()
                .WithMessage(x => $"Instrument '{x.Identifier}' must have a top quota.");

            RuleFor(x => x.DryType)
              .Null()
              .WithMessage(x => $"Instrument '{x.Identifier}' cannot have a dry type.");

            RuleFor(x => x.Measurements)
                .Empty()
                .WithMessage(x => $"Instrument '{x.Identifier}' cannot have measurements.");

            RuleForEach(x => x.SecurityLevels)
                .Must(x => x.Axis.HasValue && ((Axis)x.Axis).IsValidEnumValue())
                .When(x => x.SecurityLevels != null)
                .WithMessage(x => $"Instrument '{x.Identifier}' must have an axis in security level and the axis value needs to be valid.");

            RuleFor(x => x)
                .Must(x =>
                {
                    if (x.SecurityLevels == null)
                    {
                        return true;
                    }
    
                    var hasRepeatedAxis = x.SecurityLevels
                        .GroupBy(y => y.Axis)
                        .Any(y => y.Count() > 1);

                    return !hasRepeatedAxis;
                })
                .WithMessage(x => $"Instrument '{x.Identifier}' cannot have repeated axis for security levels.");

            RuleForEach(x => x.SecurityLevels)
                .Must(x => !x.MaximumDailyRainfall.HasValue && !x.RainIntensity.HasValue)
                .When(x => x.SecurityLevels != null)
                .WithMessage(x => $"Instrument '{x.Identifier}' cannot have security levels with maximum daily rainfall or rain intensity.");

            RuleFor(x => x.LinimetricRulerPosition)
                .Null()
                .WithMessage(x => $"Instrument '{x.Identifier}' cannot have a linimetric ruler position.");
        }
    }
}
