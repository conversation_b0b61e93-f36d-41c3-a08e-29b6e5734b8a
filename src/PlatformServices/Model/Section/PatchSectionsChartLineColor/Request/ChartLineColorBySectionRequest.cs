using System;
using System.Text.Json.Serialization;

namespace Model.Section.PatchSectionsChartLineColor.Request
{
    public sealed class ChartLineColorBySectionRequest
    {
        [JsonPropertyName("id")]
        public Guid Id { get; set; }

        [JsonPropertyName("chart_line_color")]
        public string ChartLineColor { get; set; }

        public override bool Equals(object obj)
        {
            return obj is ChartLineColorBySectionRequest colorBySection
                && colorBySection.Id == Id;
        }

        public override int GetHashCode()
        {
            return Id.GetHashCode();
        }
    }
}
